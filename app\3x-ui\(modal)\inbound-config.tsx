import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Text } from '@/components/ui/text';
import { useTranslation } from '@/hooks/useTranslation';

import { BottomSheetBackdrop, BottomSheetModal, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { Switch } from '@/components/ui/switch';
import { SelectionGroup } from '@/components/selection-group';
import BottomSheetKeyboardAwareScrollView from '@/components/BottomSheetKeyboardAwareScrollView';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';

import { useThemeColor } from '@/hooks/useThemeColor';
import { useAppStore } from '@/lib/store';
import { Protocols, InboundProtocol, InboundConfig, Vless<PERSON>ser, VmessUser, TrojanUser, ThreeXUIServerConfig } from '@/panels/3x-ui/types';
import { ThreeXUIConfig } from '@/lib/types';
import { router, useLocalSearchParams } from 'expo-router';
import { Edit, Trash2, RefreshCcw, PlusCircle, Clipboard } from 'lucide-react-native';
import * as Crypto from 'expo-crypto';
import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { ScrollView, StyleSheet, View, Alert, TouchableOpacity, Platform, Modal, SafeAreaView } from 'react-native';
import { FullWindowOverlay } from "react-native-screens";
import { generateRealityKeys, generateRandomShortIds as utilGenerateRandomShortIds, generateRandomBase64Key, generateWireguardKeys, generateTLSCertificate, smartFetch } from '@/lib/utils';
import { useNavigation } from '@react-navigation/native';
import * as ClipboardAPI from 'expo-clipboard';
import { isObject, merge, mergeWith } from 'lodash-es';
import { getThreeXUIInboundList } from '@/panels/3x-ui/utils';

const WindowOverlay = Platform.OS === "ios" ? FullWindowOverlay : React.Fragment as any

export default function InboundConfigScreen() {
  const { configId, id } = useLocalSearchParams<{ configId: string; id?: string }>();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');
  const navigation = useNavigation();

  const { getServerConfig, setServerConfig, configs } = useAppStore();
  const { t } = useTranslation();

  // 新的表单状态 - 每个都是对象状态
  const [network, setNetwork] = useState("raw");
  const [security, setSecurity] = useState("none");
  const [tlsSettings, setTlsSettings] = useState({});
  const [realitySettings, setRealitySettings] = useState({});
  const [rawSettings, setRawSettings] = useState({});
  const [xhttpSettings, setXhttpSettings] = useState({});
  const [kcpSettings, setKcpSettings] = useState({});
  const [grpcSettings, setGrpcSettings] = useState({});
  const [wsSettings, setWsSettings] = useState({});
  const [httpupgradeSettings, setHttpupgradeSettings] = useState({});
  const [sockopt, setSockopt] = useState({});
  const [listen, setListen] = useState("127.0.0.1");
  const [port, setPort] = useState("1080");
  const [protocol, setProtocol] = useState<string>(Protocols.VLESS);
  const [settings, setSettings] = useState({});
  const [sniffing, setSniffing] = useState({});
  const [expiryTime, setExpiryTime] = useState('');
  const [totalTraffic, setTotalTraffic] = useState('');

  // 用户管理状态
  const [users, setUsers] = useState<(VlessUser | VmessUser | TrojanUser)[]>([]);
  const [showUserDialog, setShowUserDialog] = useState(false);
  const [editingUser, setEditingUser] = useState<(VlessUser | VmessUser | TrojanUser) | null>(null);
  const [userEmail, setUserEmail] = useState('');
  const [userId, setUserId] = useState('');
  const [userPassword, setUserPassword] = useState('');
  const [userFlow, setUserFlow] = useState('');
  const [enableXtlsFlow, setEnableXtlsFlow] = useState(false);

  // HTTP/SOCKS 设置状态
  const [httpUseAuth, setHttpUseAuth] = useState(false);
  const [httpUsername, setHttpUsername] = useState('');
  const [httpPassword, setHttpPassword] = useState('');
  const [allowTransparent, setAllowTransparent] = useState(false);
  const [socksUseAuth, setSocksUseAuth] = useState(false);
  const [socksUsername, setSocksUsername] = useState('');
  const [socksPassword, setSocksPassword] = useState('');
  const [socksUdp, setSocksUdp] = useState(false);
  const [socksIp, setSocksIp] = useState('127.0.0.1');

  // Dokodemo 设置状态
  const [dokoAddress, setDokoAddress] = useState('*******');
  const [dokoPort, setDokoPort] = useState('53');
  const [dokoNetwork, setDokoNetwork] = useState('tcp');
  const [followRedirect, setFollowRedirect] = useState(false);

  // WireGuard 设置状态
  const [wgSecretKey, setWgSecretKey] = useState('');
  const [wgPublicKey, setWgPublicKey] = useState('');
  const [wgPeers, setWgPeers] = useState<any[]>([]);
  const [wgMtu, setWgMtu] = useState('1420');

  // TCP HTTP 伪装配置状态
  const [tcpHttpRequestVersion, setTcpHttpRequestVersion] = useState('1.1');
  const [tcpHttpRequestMethod, setTcpHttpRequestMethod] = useState('GET');
  const [tcpHttpResponseVersion, setTcpHttpResponseVersion] = useState('1.1');
  const [tcpHttpResponseStatus, setTcpHttpResponseStatus] = useState('200');
  const [tcpHttpResponseReason, setTcpHttpResponseReason] = useState('OK');

  // Fallback 管理状态
  const [fallbacks, setFallbacks] = useState<any[]>([]);
  const [showFallbackDialog, setShowFallbackDialog] = useState(false);
  const [editingFallback, setEditingFallback] = useState<any | null>(null);
  const [fallbackName, setFallbackName] = useState('');
  const [fallbackAlpn, setFallbackAlpn] = useState('');
  const [fallbackPath, setFallbackPath] = useState('');
  const [fallbackDest, setFallbackDest] = useState('80');
  const [fallbackXver, setFallbackXver] = useState('0');

  // Peer 管理状态
  const [showPeerDialog, setShowPeerDialog] = useState(false);
  const [editingPeer, setEditingPeer] = useState<any | null>(null);
  const [peerPrivateKey, setPeerPrivateKey] = useState('');
  const [peerPublicKey, setPeerPublicKey] = useState('');
  const [peerAllowedIPs, setPeerAllowedIPs] = useState('0.0.0.0/0, ::/0');

  // 传输设置状态
  const [transportType, setTransportType] = useState('tcp');

  // Bottom Sheet refs
  const transportBottomSheetRef = useRef<BottomSheetModal>(null);
  const securityBottomSheetRef = useRef<BottomSheetModal>(null);
  const sockOptBottomSheetRef = useRef<BottomSheetModal>(null);
  const sniffingBottomSheetRef = useRef<BottomSheetModal>(null);
  const certBottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['50%', '80%'], []);
  const snapPoint = useMemo(() => ['50%'], []);

  // TCP 设置
  const [tcpHeaderType, setTcpHeaderType] = useState('none');
  const [tcpHttpPaths, setTcpHttpPaths] = useState<string[]>(['/']);
  const [tcpHttpRequestHeaders, setTcpHttpRequestHeaders] = useState<Array<{key: string, value: string}>>([
    { key: 'Host', value: 'www.baidu.com' },
    { key: 'Host', value: 'www.bing.com' },
    { key: 'User-Agent', value: 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.143 Safari/537.36' },
    { key: 'User-Agent', value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_0_2 like Mac OS X) AppleWebKit/601.1 (KHTML, like Gecko) CriOS/53.0.2785.109 Mobile/14A456 Safari/601.1.46' },
    { key: 'Accept-Encoding', value: 'gzip, deflate' },
    { key: 'Connection', value: 'keep-alive' },
    { key: 'Pragma', value: 'no-cache' }
  ]);
  const [tcpHttpResponseHeaders, setTcpHttpResponseHeaders] = useState<Array<{key: string, value: string}>>([
    { key: 'Content-Type', value: 'application/octet-stream' },
    { key: 'Content-Type', value: 'video/mpeg' },
    { key: 'Transfer-Encoding', value: 'chunked' },
    { key: 'Connection', value: 'keep-alive' },
    { key: 'Pragma', value: 'no-cache' }
  ]);

  // XHTTP 设置
  const [xhttpPath, setXhttpPath] = useState('/');
  const [xhttpHost, setXhttpHost] = useState('');
  const [xhttpMode, setXhttpMode] = useState('auto');
  const [xhttpHeaders, setXhttpHeaders] = useState<Array<{key: string, value: string}>>([]);
  const [xhttpXPaddingBytes, setXhttpXPaddingBytes] = useState('100-1000');
  const [xhttpNoSSEHeader, setXhttpNoSSEHeader] = useState(false);
  const [xhttpScMaxEachPostBytes, setXhttpScMaxEachPostBytes] = useState('1000000');
  const [xhttpScMaxBufferedPosts, setXhttpScMaxBufferedPosts] = useState('30');
  const [xhttpScStreamUpServerSecs, setXhttpScStreamUpServerSecs] = useState('20-80');

  // mKCP 设置
  const [mkcpMtu, setMkcpMtu] = useState('1350');
  const [mkcpTti, setMkcpTti] = useState('20');
  const [mkcpUplinkCapacity, setMkcpUplinkCapacity] = useState('5');
  const [mkcpDownlinkCapacity, setMkcpDownlinkCapacity] = useState('20');
  const [mkcpCongestion, setMkcpCongestion] = useState(false);
  const [mkcpReadBufferSize, setMkcpReadBufferSize] = useState('1');
  const [mkcpWriteBufferSize, setMkcpWriteBufferSize] = useState('1');
  const [mkcpHeaderType, setMkcpHeaderType] = useState('none');
  const [mkcpHeaderDomain, setMkcpHeaderDomain] = useState('');
  const [mkcpSeed, setMkcpSeed] = useState(() => {
    // 生成16位字母数字随机值
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 16; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  });

  // gRPC 设置
  const [grpcAuthority, setGrpcAuthority] = useState('grpc.example.com');
  const [grpcServiceName, setGrpcServiceName] = useState('name');
  const [grpcInitialWindowsSize, setGrpcInitialWindowsSize] = useState('0');

  // WebSocket 设置
  const [wsAcceptProxyProtocol, setWsAcceptProxyProtocol] = useState(false);
  const [wsPath, setWsPath] = useState('/');
  const [wsHost, setWsHost] = useState('xray.com');
  const [wsHeartbeatPeriod, setWsHeartbeatPeriod] = useState('0');

  // HTTP Upgrade 设置
  const [httpUpgradeAcceptProxyProtocol, setHttpUpgradeAcceptProxyProtocol] = useState(false);
  const [httpUpgradePath, setHttpUpgradePath] = useState('/');
  const [httpUpgradeHost, setHttpUpgradeHost] = useState('xray.com');

  // TCP 设置
  const [tcpAcceptProxyProtocol, setTcpAcceptProxyProtocol] = useState(false);

  // 安全设置状态
  const [securityType, setSecurityType] = useState('none');

  // TLS 设置
  const [tlsRejectUnknownSni, setTlsRejectUnknownSni] = useState(false);
  const [tlsAllowInsecure, setTlsAllowInsecure] = useState(false);
  const [tlsAlpn, setTlsAlpn] = useState<string[]>(['h2', 'http/1.1']);
  const [tlsMinVersion, setTlsMinVersion] = useState('1.2');
  const [tlsMaxVersion, setTlsMaxVersion] = useState('1.3');
  const [tlsCertificates, setTlsCertificates] = useState<any[]>([]);
  const [tlsDisableSystemRoot, setTlsDisableSystemRoot] = useState(false);
  const [tlsEnableSessionResumption, setTlsEnableSessionResumption] = useState(false);

  // Reality 设置
  const [realityTarget, setRealityTarget] = useState('example.com:443');
  const [realityXver, setRealityXver] = useState(0);
  const [realityPrivateKey, setRealityPrivateKey] = useState('');
  const [realityPublicKey, setRealityPublicKey] = useState('');
  const [realityMaxTimeDiff, setRealityMaxTimeDiff] = useState(0);
  const [realityShortIds, setRealityShortIds] = useState<string[]>(['', 'abcdef12']);
  const [realityServerNames, setRealityServerNames] = useState('example.com,www.example.com');
  const [realityLimitFallbackUpload, setRealityLimitFallbackUpload] = useState({
    afterBytes: 0,
    bytesPerSec: 0,
    burstBytesPerSec: 0
  });
  const [realityLimitFallbackDownload, setRealityLimitFallbackDownload] = useState({
    afterBytes: 0,
    bytesPerSec: 0,
    burstBytesPerSec: 0
  });

  // 证书管理状态
  const [editingCert, setEditingCert] = useState<any>(null);
  const [certAddMethod, setCertAddMethod] = useState<'file' | 'content'>('content');
  const [certFile, setCertFile] = useState('');
  const [certKeyFile, setCertKeyFile] = useState('');
  const [certContent, setCertContent] = useState('');
  const [certKeyContent, setCertKeyContent] = useState('');

  // SockOpt 设置状态
  const [enableSockOpt, setEnableSockOpt] = useState(false);
  const [sockOptMark, setSockOptMark] = useState('0');
  const [sockOptTcpMaxSeg, setSockOptTcpMaxSeg] = useState('1440');
  const [sockOptTcpFastOpen, setSockOptTcpFastOpen] = useState(false);
  const [sockOptTproxy, setSockOptTproxy] = useState('off');
  const [sockOptDomainStrategy, setSockOptDomainStrategy] = useState('AsIs');
  const [sockOptDialerProxy, setSockOptDialerProxy] = useState('');
  const [sockOptAcceptProxyProtocol, setSockOptAcceptProxyProtocol] = useState(false);
  const [sockOptTcpKeepAliveInterval, setSockOptTcpKeepAliveInterval] = useState('0');
  const [sockOptTcpKeepAliveIdle, setSockOptTcpKeepAliveIdle] = useState('300');
  const [sockOptTcpUserTimeout, setSockOptTcpUserTimeout] = useState('10000');
  const [sockOptTcpCongestion, setSockOptTcpCongestion] = useState('bbr');
  const [sockOptInterface, setSockOptInterface] = useState('');
  const [sockOptV6Only, setSockOptV6Only] = useState(false);
  const [sockOptTcpWindowClamp, setSockOptTcpWindowClamp] = useState('600');
  const [sockOptAddressPortStrategy, setSockOptAddressPortStrategy] = useState('none');

  // Sniffing 设置状态
  const [enableSniffing, setEnableSniffing] = useState(false);
  const [sniffingDestOverride, setSniffingDestOverride] = useState<string[]>(['http', 'tls', 'fakedns']);
  const [sniffingMetadataOnly, setSniffingMetadataOnly] = useState(false);
  const [sniffingDomainsExcluded, setSniffingDomainsExcluded] = useState<string[]>([]);
  const [sniffingRouteOnly, setSniffingRouteOnly] = useState(false);

  // 保存状态
  const [isSaving, setIsSaving] = useState(false);



  // 检查是否为编辑模式
  const isEditMode = !!id;
  const originalInbound = isEditMode ? getServerConfig(configId).inbounds.find((inb: InboundConfig) => inb.tag === id) : null

  // 跟踪是否已经初始化过用户
  const hasInitializedUsers = useRef(false);

  // 渲染底部弹窗背景
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // 生成Reality密钥对
  const handleGenerateRealityKeys = async () => {
    try {
      const keys = await generateRealityKeys();
      setRealityPrivateKey(keys.privateKey);
      setRealityPublicKey(keys.publicKey);
    } catch (error) {
      Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorGenerateKeysFailed'));
    }
  };

  // 生成随机ShortIDs
  const generateRandomShortIds = () => {
    const shortIds = utilGenerateRandomShortIds();
    setRealityShortIds(shortIds);
  };

  // 生成Shadowsocks密码
  const handleGenerateSSPassword = () => {
    const bytes = getPasswordBytesForMethod(ssMethod);
    const password = generateRandomBase64Key(bytes);
    setSsPassword(password);
  };

  const handleGenerateSSUserPassword = () => {
    const bytes = getPasswordBytesForMethod(ssMethod);
    const password = generateRandomBase64Key(bytes);
    setUserPassword(password);
  };

  // 证书管理函数
  const handleAddCert = () => {
    setCertAddMethod('content');
    setCertFile('');
    setCertKeyFile('');
    setCertContent('');
    setCertKeyContent('');
    setEditingCert(null);
    certBottomSheetRef.current?.present();
  };

  const handleEditCert = (cert: any, index: number) => {
    setCertAddMethod(cert.certificateFile ? 'file' : 'content');
    setCertFile(cert.certificateFile || '');
    setCertKeyFile(cert.keyFile || '');
    setCertContent(cert.certificate ? cert.certificate.join('\n') : '');
    setCertKeyContent(cert.key ? cert.key.join('\n') : '');
    setEditingCert({ ...cert, index });
    certBottomSheetRef.current?.present();
  };

  const handleDeleteCert = (index: number) => {
    Alert.alert(
      t('threeXUI.inboundConfig.confirmDelete'),
      t('threeXUI.inboundConfig.confirmDeleteCert'),
      [
        { text: t('threeXUI.inboundConfig.cancel'), style: 'cancel' },
        {
          text: t('threeXUI.inboundConfig.delete'),
          style: 'destructive',
          onPress: () => {
            const newCerts = [...tlsCertificates];
            newCerts.splice(index, 1);
            setTlsCertificates(newCerts);
          }
        }
      ]
    );
  };

  const handleSaveCert = () => {
    if (certAddMethod === 'file') {
      if (!certFile.trim() || !certKeyFile.trim()) {
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorCertPathRequired'));
        return;
      }
    } else {
      if (!certContent.trim() || !certKeyContent.trim()) {
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorCertContentRequired'));
        return;
      }
    }

    const newCert: any = {
      ocspStapling: 0,
      oneTimeLoading: false,
      usage: 'encipherment',
      buildChain: false
    };

    if (certAddMethod === 'file') {
      newCert.certificateFile = certFile;
      newCert.keyFile = certKeyFile;
    } else {
      newCert.certificate = certContent.split(/\r?\n/).filter(line => line.trim());
      newCert.key = certKeyContent.split(/\r?\n/).filter(line => line.trim());
    }

    if (editingCert && typeof editingCert.index === 'number') {
      // 编辑现有证书：将原证书配置与新配置合并，保留无法编辑的属性
      const newCerts = [...tlsCertificates];
      const originalCert = newCerts[editingCert.index];
      // 合并原证书和新证书配置，新配置覆盖原配置
      newCerts[editingCert.index] = { ...originalCert, ...newCert };
      setTlsCertificates(newCerts);
    } else {
      // 添加新证书
      setTlsCertificates([...tlsCertificates, newCert]);
    }

    certBottomSheetRef.current?.dismiss();
  };

  // 加载配置的函数，接受JSON参数
  const loadEditModeConfig = (inbound: InboundConfig) => {

    // 基本配置
    setListen(inbound.listen);
    setPort(inbound.port.toString());
    setProtocol(inbound.protocol);

    // 处理有效期逻辑
    if (inbound.expiryTime && inbound.expiryTime !== 0) {
      const currentTime = Date.now();
      if (inbound.expiryTime <= currentTime) {
        // 已过期，设为0
        setExpiryTime('0');
      } else {
        // 计算剩余天数
        const remainingMilliseconds = inbound.expiryTime - currentTime;
        const remainingDays = Math.ceil(remainingMilliseconds / (24 * 60 * 60 * 1000));
        setExpiryTime(remainingDays.toString());
      }
    } else {
      // 如果为0或未设置，都代表永久有效，在UI中显示为留空
      setExpiryTime('');
    }

    setTotalTraffic(inbound.totalTraffic?.toString() || '');

    // 加载 Settings 配置
    if (inbound.settings) {
      const settings = inbound.settings;

      // 用户配置 (VLESS, VMess, Trojan, Shadowsocks)
      if (settings.clients) {
        // 为没有email字段的client自动添加随机email
        const clientsWithEmail = settings.clients.map((client: any) => {
          if (!client.email) {
            return {
              ...client,
              email: Math.random().toString(36).substring(2, 14)
            };
          }
          return client;
        });
        setUsers(clientsWithEmail);
      }

      // Shadowsocks 特定配置
      if (inbound.protocol === 'shadowsocks') {
        setSsMethod(settings.method || '2022-blake3-aes-256-gcm');
        setSsPassword(settings.password || '');
        setSsNetwork(settings.network || 'tcp,udp');
      }

      // HTTP 配置
      if (inbound.protocol === 'http') {
        setHttpUseAuth(settings.accounts && settings.accounts.length > 0);
        if (settings.accounts && settings.accounts.length > 0) {
          setHttpUsername(settings.accounts[0].user || '');
          setHttpPassword(settings.accounts[0].pass || '');
        }
        setAllowTransparent(settings.allowTransparent || false);
      }

      // SOCKS 配置
      if (inbound.protocol === 'socks') {
        setSocksUseAuth(settings.auth === 'password');
        if (settings.accounts && settings.accounts.length > 0) {
          setSocksUsername(settings.accounts[0].user || '');
          setSocksPassword(settings.accounts[0].pass || '');
        }
        setSocksUdp(settings.udp || false);
        setSocksIp(settings.ip || '127.0.0.1');
      }

      // Dokodemo 配置
      if (inbound.protocol === 'dokodemo-door') {
        setDokoAddress(settings.address || '*******');
        setDokoPort(settings.port?.toString() || '53');
        setDokoNetwork(settings.network || 'tcp');
        setFollowRedirect(settings.followRedirect || false);
      }

      // WireGuard 配置
      if (inbound.protocol === 'wireguard') {
        setWgSecretKey(settings.secretKey || '');
        setWgPublicKey(settings.publicKey || '')
        setWgPeers(settings.peers || []);
        setWgMtu(settings.mtu?.toString() || '1420');
      }

      // Fallbacks 配置 (VLESS, Trojan)
      if (settings.fallbacks) {
        setFallbacks(settings.fallbacks);
      }
    }

    // 加载 Sniffing 配置
    if (inbound.sniffing) {
      setEnableSniffing(inbound.sniffing.enabled);
      setSniffingDestOverride(inbound.sniffing.destOverride || ['http', 'tls', 'fakedns']);
      setSniffingMetadataOnly(inbound.sniffing.metadataOnly || false);
      setSniffingDomainsExcluded(inbound.sniffing.domainsExcluded || []);
      setSniffingRouteOnly(inbound.sniffing.routeOnly || false);
    }

    // 加载 StreamSettings 配置
    if (inbound.streamSettings) {
      const streamSettings = inbound.streamSettings;

      // 传输类型
      if (streamSettings.network) {
        setTransportType(streamSettings.network);
      }

      // TCP 设置
      if (streamSettings.tcpSettings) {
        setTcpAcceptProxyProtocol(streamSettings.tcpSettings.acceptProxyProtocol || false);
        if (streamSettings.tcpSettings.header) {
          setTcpHeaderType(streamSettings.tcpSettings.header.type || 'none');
          if (streamSettings.tcpSettings.header.type === 'http') {
            const request = streamSettings.tcpSettings.header.request;
            const response = streamSettings.tcpSettings.header.response;

            if (request) {
              setTcpHttpRequestVersion(request.version || '1.1');
              setTcpHttpRequestMethod(request.method || 'GET');
              setTcpHttpPaths(request.path || ['/']);

              if (request.headers) {
                const headers: Array<{key: string, value: string}> = [];
                Object.entries(request.headers).forEach(([key, values]) => {
                  if (Array.isArray(values)) {
                    values.forEach(value => headers.push({ key, value }));
                  }
                });
                setTcpHttpRequestHeaders(headers);
              }
            }

            if (response) {
              setTcpHttpResponseVersion(response.version || '1.1');
              setTcpHttpResponseStatus(response.status || '200');
              setTcpHttpResponseReason(response.reason || 'OK');

              if (response.headers) {
                const headers: Array<{key: string, value: string}> = [];
                Object.entries(response.headers).forEach(([key, values]) => {
                  if (Array.isArray(values)) {
                    values.forEach(value => headers.push({ key, value }));
                  }
                });
                setTcpHttpResponseHeaders(headers);
              }
            }
          }
        }
      }

      // XHTTP 设置
      if (streamSettings.xhttpSettings) {
        const xhttp = streamSettings.xhttpSettings;
        setXhttpHost(xhttp.host || '');
        setXhttpPath(xhttp.path || '/');
        setXhttpMode(xhttp.mode || 'auto');

        if (xhttp.extra) {
          if (xhttp.extra.headers) {
            const headers: Array<{key: string, value: string}> = [];
            Object.entries(xhttp.extra.headers).forEach(([key, value]) => {
              headers.push({ key, value: value as string });
            });
            setXhttpHeaders(headers);
          }
          setXhttpXPaddingBytes(xhttp.extra.xPaddingBytes || '100-1000');
          setXhttpNoSSEHeader(xhttp.extra.noSSEHeader || false);
          setXhttpScMaxEachPostBytes(xhttp.extra.scMaxEachPostBytes?.toString() || '1000000');
          setXhttpScMaxBufferedPosts(xhttp.extra.scMaxBufferedPosts?.toString() || '30');
          setXhttpScStreamUpServerSecs(xhttp.extra.scStreamUpServerSecs || '20-80');
        }
      }

      // mKCP 设置
      if (streamSettings.kcpSettings) {
        const kcp = streamSettings.kcpSettings;
        setMkcpMtu(kcp.mtu?.toString() || '1350');
        setMkcpTti(kcp.tti?.toString() || '20');
        setMkcpUplinkCapacity(kcp.uplinkCapacity?.toString() || '5');
        setMkcpDownlinkCapacity(kcp.downlinkCapacity?.toString() || '20');
        setMkcpCongestion(kcp.congestion || false);
        setMkcpReadBufferSize(kcp.readBufferSize?.toString() || '1');
        setMkcpWriteBufferSize(kcp.writeBufferSize?.toString() || '1');

        if (kcp.header) {
          setMkcpHeaderType(kcp.header.type || 'none');
          setMkcpHeaderDomain(kcp.header.domain || '');
        }
        setMkcpSeed(kcp.seed || '');
      }

      // gRPC 设置
      if (streamSettings.grpcSettings) {
        const grpc = streamSettings.grpcSettings;
        setGrpcAuthority(grpc.authority || 'grpc.example.com');
        setGrpcServiceName(grpc.serviceName || 'name');
        setGrpcInitialWindowsSize(grpc.initial_windows_size?.toString() || '0');
      }

      // WebSocket 设置
      if (streamSettings.wsSettings) {
        const ws = streamSettings.wsSettings;
        setWsAcceptProxyProtocol(ws.acceptProxyProtocol || false);
        setWsPath(ws.path || '/');
        setWsHost(ws.host || '');
        setWsHeartbeatPeriod(ws.heartbeatPeriod?.toString() || '0');
      }

      // HTTP Upgrade 设置
      if (streamSettings.httpupgradeSettings) {
        const httpUpgrade = streamSettings.httpupgradeSettings;
        setHttpUpgradeAcceptProxyProtocol(httpUpgrade.acceptProxyProtocol || false);
        setHttpUpgradePath(httpUpgrade.path || '/');
        setHttpUpgradeHost(httpUpgrade.host || 'xray.com');
      }

      // 安全设置
      if (streamSettings.security) {
        setSecurityType(streamSettings.security);

        // TLS 设置
        if (streamSettings.security === 'tls' && streamSettings.tlsSettings) {
          const tls = streamSettings.tlsSettings;
          setTlsRejectUnknownSni(tls.rejectUnknownSni || false);
          setTlsAllowInsecure(tls.allowInsecure || false);
          setTlsAlpn(tls.alpn || ['h2', 'http/1.1']);
          setTlsMinVersion(tls.minVersion || '1.2');
          setTlsMaxVersion(tls.maxVersion || '1.3');
          setTlsCertificates(tls.certificates || []);
          setTlsDisableSystemRoot(tls.disableSystemRoot || false);
          setTlsEnableSessionResumption(tls.enableSessionResumption || false);
        }

        // Reality 设置
        if (streamSettings.security === 'reality' && streamSettings.realitySettings) {
          const reality = streamSettings.realitySettings;
          setRealityTarget(reality.target || reality.dest || 'example.com:443');
          setRealityXver(reality.xver || 0);
          setRealityPrivateKey(reality.privateKey || '');
          // 从settings对象中读取publicKey，如果没有则从旧的位置读取
          setRealityPublicKey(reality.settings?.publicKey || reality.publicKey || '');
          setRealityMaxTimeDiff(reality.maxTimeDiff || 0);
          setRealityShortIds(reality.shortIds || ['', 'abcdef12']);
          setRealityServerNames(Array.isArray(reality.serverNames) ? reality.serverNames.join(',') : reality.serverNames || 'example.com,www.example.com');
          setRealityLimitFallbackUpload(reality.limitFallbackUpload || { afterBytes: 0, bytesPerSec: 0, burstBytesPerSec: 0 });
          setRealityLimitFallbackDownload(reality.limitFallbackDownload || { afterBytes: 0, bytesPerSec: 0, burstBytesPerSec: 0 });
        }
      }

      // SockOpt 设置
      if (streamSettings.sockopt) {
        setEnableSockOpt(true);
        const sockopt = streamSettings.sockopt;
        setSockOptMark(sockopt.mark?.toString() || '0');
        setSockOptTcpMaxSeg(sockopt.tcpMaxSeg?.toString() || '1440');
        setSockOptTcpFastOpen(sockopt.tcpFastOpen || false);
        setSockOptTproxy(sockopt.tproxy || 'off');
        setSockOptDomainStrategy(sockopt.domainStrategy || 'AsIs');
        setSockOptDialerProxy(sockopt.dialerProxy || '');
        setSockOptAcceptProxyProtocol(sockopt.acceptProxyProtocol || false);
        setSockOptTcpKeepAliveInterval(sockopt.tcpKeepAliveInterval?.toString() || '0');
        setSockOptTcpKeepAliveIdle(sockopt.tcpKeepAliveIdle?.toString() || '300');
        setSockOptTcpUserTimeout(sockopt.tcpUserTimeout?.toString() || '10000');
        setSockOptTcpCongestion(sockopt.tcpcongestion || 'bbr');
        setSockOptInterface(sockopt.interface || '');
        setSockOptV6Only(sockopt.V6Only || false);
        setSockOptTcpWindowClamp(sockopt.tcpWindowClamp?.toString() || '600');
        setSockOptAddressPortStrategy(sockopt.addressPortStrategy || 'none');
      }
    }
  };

  // 剪切板导入功能
  const handleClipboardImport = async () => {
    try {
      const clipboardText = await ClipboardAPI.getStringAsync();
      if (!clipboardText.trim()) {
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.importErrorEmpty'));
        return;
      }

      const inboundConfig = JSON.parse(clipboardText);

      // 验证是否为有效的入站配置
      if (!inboundConfig.protocol || !inboundConfig.port) {
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.importErrorInvalid'));
        return;
      }

      // 导入配置到表单
      loadEditModeConfig(inboundConfig);
      Alert.alert(t('common.success'), t('threeXUI.inboundConfig.importSuccess'));
    } catch (error) {
      Alert.alert(t('common.error'), t('threeXUI.inboundConfig.importErrorFormat'));
    }
  };

  // 设置header右侧按钮（只在非编辑模式下显示）
  useEffect(() => {
    if (!isEditMode) {
      navigation.setOptions({
        headerRight: () => (
          <TouchableOpacity onPress={handleClipboardImport} style={{ marginRight: 4 }}>
            <Clipboard size={20} color={textColor} />
          </TouchableOpacity>
        ),
        title: t('threeXUI.inboundConfig.addTitle')
      });
    } else {
      navigation.setOptions({
        headerRight: undefined,
        title: t('threeXUI.inboundConfig.editTitle')
      });
    }
  }, [navigation, textColor, isEditMode, t]);

  // 初始化时加载编辑模式配置
  useEffect(() => {
    if (!configId) {
      Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorConfigIdNotFound'));
      router.back();
      return;
    }

    if (originalInbound) {
      // 编辑模式：从存储中获取配置并加载
      loadEditModeConfig(originalInbound);
    }
  }, [configId, id, isEditMode]);

  // 新建模式时自动添加用户（仅在初始化时）
  useEffect(() => {
    if (!isEditMode && !hasInitializedUsers.current && ['vmess', 'vless', 'trojan', 'shadowsocks'].includes(protocol)) {
      const newUser = createRandomUser(protocol);
      if (newUser) {
        setUsers([newUser]);
        hasInitializedUsers.current = true;
      }
    }
  }, [isEditMode, protocol]); // 不依赖 users.length，避免无限循环



  // 生成随机用户数据
  const generateRandomUserData = () => {
    const randomEmail = Math.random().toString(36).substring(2, 14);
    const randomId = Crypto.randomUUID();

    let randomPassword;
    if (protocol === Protocols.SHADOWSOCKS) {
      // 为Shadowsocks生成base64密码，根据当前加密方法确定字节长度
      const bytes = getPasswordBytesForMethod(ssMethod);
      randomPassword = generateRandomBase64Key(bytes);
    } else {
      // 其他协议使用简单的随机字符串
      randomPassword = Math.random().toString(36).substring(2, 14);
    }

    setUserEmail(randomEmail);
    setUserId(randomId);
    setUserPassword(randomPassword);
  };

  // 添加用户 - 弹出对话框
  const handleAddUser = () => {
    generateRandomUserData();
    setEditingUser(null);
    setUserFlow('');
    setEnableXtlsFlow(false);
    setShowUserDialog(true);
  };

  // 根据加密方法获取密码字节长度
  const getPasswordBytesForMethod = (method: string): number => {
    // 对于2022-blake3-aes-256-gcm和2022-blake3-chacha20-poly1305使用32字节
    if (method === '2022-blake3-aes-256-gcm' || method === '2022-blake3-chacha20-poly1305') {
      return 32;
    }
    return 16; // 默认16字节
  };

  // 处理Shadowsocks加密方法变化
  const handleSsMethodChange = (option: any) => {
    const newMethod = option?.value || option;
    setSsMethod(newMethod as string);

    // 如果当前协议是Shadowsocks且有用户，需要重新生成用户密码
    if (protocol === Protocols.SHADOWSOCKS && users.length > 0) {
      const updatedUsers = users.map(user => {
        if ('password' in user) {
          const bytes = getPasswordBytesForMethod(newMethod);
          const newPassword = generateRandomBase64Key(bytes);
          return { ...user, password: newPassword };
        }
        return user;
      });
      setUsers(updatedUsers);
    }
  };

  // 创建随机用户对象（纯函数，不修改状态）
  const createRandomUser = (targetProtocol: string, targetSsMethod?: string): VlessUser | VmessUser | TrojanUser | any | null => {
    const randomEmail = Math.random().toString(36).substring(2, 14);
    const randomId = Crypto.randomUUID();

    let newUser: VlessUser | VmessUser | TrojanUser | any;

    if (targetProtocol === Protocols.VLESS) {
      newUser = {
        id: randomId,
        email: randomEmail
      } as VlessUser;
    } else if (targetProtocol === Protocols.VMESS) {
      newUser = {
        id: randomId,
        email: randomEmail
      } as VmessUser;
    } else if (targetProtocol === Protocols.TROJAN) {
      const randomPassword = Math.random().toString(36).substring(2, 14);
      newUser = {
        password: randomPassword,
        email: randomEmail
      } as TrojanUser;
    } else if (targetProtocol === Protocols.SHADOWSOCKS) {
      // 为Shadowsocks生成随机密码，根据指定或当前加密方法确定字节长度
      const method = targetSsMethod || ssMethod;
      const bytes = getPasswordBytesForMethod(method);
      const randomPassword = generateRandomBase64Key(bytes);
      newUser = {
        password: randomPassword,
        email: randomEmail
      };
    } else {
      return null;
    }

    newUser.enable = true;
    newUser.subId = encodeURIComponent(newUser.email);
    return newUser;
  };



  // 编辑用户
  const handleEditUser = (user: VlessUser | VmessUser | TrojanUser) => {
    setEditingUser(user);
    setUserEmail(user.email);

    if ('id' in user) {
      setUserId(user.id);
      if ('flow' in user) {
        setUserFlow(user.flow || '');
        setEnableXtlsFlow(!!user.flow);
      }
    }

    if ('password' in user) {
      setUserPassword(user.password);
    }

    setShowUserDialog(true);
  };

  // 保存用户
  const handleSaveUser = () => {
    if (!userEmail.trim()) {
      Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorEmailRequired'));
      return;
    }

    let newUser: VlessUser | VmessUser | TrojanUser | any;

    if (protocol === Protocols.VLESS) {
      if (!userId.trim()) {
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorUuidRequired'));
        return;
      }
      newUser = {
        id: userId,
        email: userEmail,
        ...(enableXtlsFlow && userFlow ? { flow: userFlow } : { flow: '' })
      } as VlessUser;
    } else if (protocol === Protocols.VMESS) {
      if (!userId.trim()) {
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorUuidRequired'));
        return;
      }
      newUser = {
        id: userId,
        email: userEmail
      } as VmessUser;
    } else if (protocol === Protocols.TROJAN) {
      if (!userPassword.trim()) {
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorPasswordRequired'));
        return;
      }
      newUser = {
        password: userPassword,
        email: userEmail
      } as TrojanUser;
    } else if (protocol === Protocols.SHADOWSOCKS) {
      if (!userPassword.trim()) {
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorPasswordRequired'));
        return;
      }
      newUser = {
        password: userPassword,
        email: userEmail
      };
    } else {
      Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorProtocolRequired'));
      return;
    }
    newUser.enable = true
    newUser.subId = encodeURIComponent(newUser.email)
    if (editingUser) {
      // 编辑模式：将被编辑用户与newUser合并，保留无法编辑的属性
      const index = users.findIndex(u => u.email === editingUser.email);
      if (index !== -1) {
        const newUsers = [...users];
        // 合并原用户和新用户数据，新用户数据覆盖原用户数据
        newUsers[index] = { ...editingUser, ...newUser };
        setUsers(newUsers);
      }
    } else {
      // 添加模式
      setUsers(prevUsers => [...prevUsers, newUser]);
    }

    setShowUserDialog(false);
  };

  // 删除用户
  const handleDeleteUser = (user: VlessUser | VmessUser | TrojanUser) => {
    Alert.alert(
      t('threeXUI.inboundConfig.confirmDelete'),
      t('threeXUI.inboundConfig.confirmDeleteUser').replace('{email}', user.email),
      [
        { text: t('threeXUI.inboundConfig.cancel'), style: 'cancel' },
        {
          text: t('threeXUI.inboundConfig.delete'),
          style: 'destructive',
          onPress: () => {
            setUsers(prevUsers => prevUsers.filter(u => u.email !== user.email));
          }
        }
      ]
    );
  };

  // Fallback 管理函数
  const handleAddFallback = () => {
    setEditingFallback(null);
    setFallbackName('');
    setFallbackAlpn('');
    setFallbackPath('');
    setFallbackDest('80');
    setFallbackXver('0');
    setShowFallbackDialog(true);
  };

  const handleEditFallback = (fallback: any, index: number) => {
    setEditingFallback({ ...fallback, index });
    setFallbackName(fallback.name || '');
    setFallbackAlpn(fallback.alpn || '');
    setFallbackPath(fallback.path || '');
    setFallbackDest(fallback.dest?.toString() || '80');
    setFallbackXver(fallback.xver?.toString() || '0');
    setShowFallbackDialog(true);
  };

  const handleSaveFallback = () => {
    const destNum = parseInt(fallbackDest);
    const xverNum = parseInt(fallbackXver);

    const newFallback = {
      name: fallbackName.trim() || '',
      alpn: fallbackAlpn.trim() || '',
      path: fallbackPath.trim() || '',
      dest: isNaN(destNum) ? 80 : destNum,
      xver: isNaN(xverNum) ? 0 : xverNum
    };

    if (editingFallback && editingFallback.index !== undefined) {
      // 编辑模式：将原回落配置与新配置合并，保留无法编辑的属性
      const newFallbacks = [...fallbacks];
      const originalFallback = newFallbacks[editingFallback.index];
      // 合并原配置和新配置，新配置覆盖原配置
      newFallbacks[editingFallback.index] = { ...originalFallback, ...newFallback };
      setFallbacks(newFallbacks);
    } else {
      // 添加模式
      setFallbacks([...fallbacks, newFallback]);
    }

    setShowFallbackDialog(false);
    setEditingFallback(null);
  };

  // XHTTP Header 管理函数
  const handleAddXhttpHeader = () => {
    setXhttpHeaders([...xhttpHeaders, { key: '', value: '' }]);
  };

  const handleUpdateXhttpHeader = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...xhttpHeaders];
    newHeaders[index][field] = value;
    setXhttpHeaders(newHeaders);
  };

  const handleRemoveXhttpHeader = (index: number) => {
    const newHeaders = [...xhttpHeaders];
    newHeaders.splice(index, 1);
    setXhttpHeaders(newHeaders);
  };

  // TLS证书生成函数
  const handleGenerateTLSCertificate = async () => {
    try {
      let host = 'localhost';
      const config = configs.find(c => c.id === configId);
      if (config?.url) {
        try {
          const url = new URL(`${config.protocol}://${config.url}`);
          host = url.hostname;
        } catch {
          // 如果URL解析失败，使用默认值
          host = 'localhost';
        }
      }
      const certPair = await generateTLSCertificate(host);
      setCertContent(certPair.certificate);
      setCertKeyContent(certPair.privateKey);
    } catch (error) {
      console.log(error)
    }
  };



  const handleDeleteFallback = (index: number) => {
    Alert.alert(
      t('threeXUI.inboundConfig.confirmDelete'),
      t('threeXUI.inboundConfig.confirmDeleteFallback'),
      [
        { text: t('threeXUI.inboundConfig.cancel'), style: 'cancel' },
        {
          text: t('threeXUI.inboundConfig.delete'),
          style: 'destructive',
          onPress: () => {
            setFallbacks(fallbacks.filter((_, i) => i !== index));
          }
        }
      ]
    );
  };

  // Peer 管理函数
  const handleAddPeer = () => {
    setEditingPeer(null);
    setPeerPrivateKey('');
    setPeerPublicKey('');
    setPeerAllowedIPs('0.0.0.0/0, ::/0');
    setShowPeerDialog(true);
  };

  const handleEditPeer = (peer: any, index: number) => {
    setEditingPeer({ ...peer, index });
    setPeerPrivateKey(peer.privateKey || '');
    setPeerPublicKey(peer.publicKey || '');
    setPeerAllowedIPs(peer.allowedIPs?.join(', ') || '0.0.0.0/0, ::/0');
    setShowPeerDialog(true);
  };

  const handleSavePeer = () => {
    if (!peerPublicKey.trim()) {
      Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorPublicKeyRequired'));
      return;
    }

    const allowedIPsArray = peerAllowedIPs.split(',').map(ip => ip.trim()).filter(ip => ip);

    const newPeer = {
      privateKey: peerPrivateKey.trim() || undefined,
      publicKey: peerPublicKey.trim(),
      allowedIPs: allowedIPsArray.length > 0 ? allowedIPsArray : ['0.0.0.0/0', '::/0']
    };

    if (editingPeer && editingPeer.index !== undefined) {
      // 编辑模式：将原Peer配置与新配置合并，保留无法编辑的属性
      const newPeers = [...wgPeers];
      const originalPeer = newPeers[editingPeer.index];
      // 合并原Peer和新Peer配置，新配置覆盖原配置
      newPeers[editingPeer.index] = { ...originalPeer, ...newPeer };
      setWgPeers(newPeers);
    } else {
      // 添加模式
      setWgPeers([...wgPeers, newPeer]);
    }

    setShowPeerDialog(false);
    setEditingPeer(null);
  };

  const handleDeletePeer = (index: number) => {
    Alert.alert(
      t('threeXUI.inboundConfig.confirmDelete'),
      t('threeXUI.inboundConfig.confirmDeletePeer'),
      [
        { text: t('threeXUI.inboundConfig.cancel'), style: 'cancel' },
        {
          text: t('threeXUI.inboundConfig.delete'),
          style: 'destructive',
          onPress: () => {
            setWgPeers(wgPeers.filter((_, i) => i !== index));
          }
        }
      ]
    );
  };

  const handleGeneratePeerKeys = async () => {
    try {
      const keys = await generateWireguardKeys();
      setPeerPrivateKey(keys.privateKey);
      setPeerPublicKey(keys.publicKey);
    } catch (error) {
      Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorGenerateKeysFailed'));
    }
  };

  // 清空设置（当协议改变时）
  const handleProtocolChange = (option: any) => {
    const newProtocol = option?.value || option;
    setProtocol(newProtocol as InboundProtocol);

    // 重置协议特定设置（需要在创建用户之前重置，确保使用正确的设置）
    setSsMethod('2022-blake3-aes-256-gcm');
    setSsPassword('');
    setSsNetwork('tcp,udp');

    // 根据新协议设置用户列表
    if (['vmess', 'vless', 'trojan', 'shadowsocks'].includes(newProtocol)) {
      // 对于Shadowsocks，需要使用重置后的默认加密方法
      const newUser = newProtocol === Protocols.SHADOWSOCKS
        ? createRandomUser(newProtocol, '2022-blake3-aes-256-gcm')
        : createRandomUser(newProtocol);
      setUsers(newUser ? [newUser] : []);
    } else {
      setUsers([]);
    }
    // Shadowsocks用户由共用的users管理，这里不需要单独重置
    setHttpUseAuth(false);
    setHttpUsername('');
    setHttpPassword('');
    setAllowTransparent(false);
    setSocksUseAuth(false);
    setSocksUsername('');
    setSocksPassword('');
    setSocksUdp(false);
    setSocksIp('127.0.0.1');
    setDokoAddress('*******');
    setDokoPort('53');
    setDokoNetwork('tcp');
    setFollowRedirect(false);
    setWgSecretKey('');
    setWgPublicKey('');
    setWgPeers([]);
    setWgMtu('1420');

    // 重置 TCP HTTP 伪装配置
    setTcpHttpRequestVersion('1.1');
    setTcpHttpRequestMethod('GET');
    setTcpHttpResponseVersion('1.1');
    setTcpHttpResponseStatus('200');
    setTcpHttpResponseReason('OK');

    // 重置回落和 Peer 管理
    setFallbacks([]);
    setShowFallbackDialog(false);
    setEditingFallback(null);
    setShowPeerDialog(false);
    setEditingPeer(null);

    // 重置传输设置
    setTransportType('tcp');
    setTcpHeaderType('none');
    setTcpAcceptProxyProtocol(false);
    setTcpHttpPaths(['/']);
    setTcpHttpRequestHeaders([
      { key: 'Host', value: 'www.baidu.com' },
      { key: 'Host', value: 'www.bing.com' },
      { key: 'User-Agent', value: 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.143 Safari/537.36' },
      { key: 'User-Agent', value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_0_2 like Mac OS X) AppleWebKit/601.1 (KHTML, like Gecko) CriOS/53.0.2785.109 Mobile/14A456 Safari/601.1.46' },
      { key: 'Accept-Encoding', value: 'gzip, deflate' },
      { key: 'Connection', value: 'keep-alive' },
      { key: 'Pragma', value: 'no-cache' }
    ]);
    setTcpHttpResponseHeaders([
      { key: 'Content-Type', value: 'application/octet-stream' },
      { key: 'Content-Type', value: 'video/mpeg' },
      { key: 'Transfer-Encoding', value: 'chunked' },
      { key: 'Connection', value: 'keep-alive' },
      { key: 'Pragma', value: 'no-cache' }
    ]);
    setXhttpPath('/');
    setXhttpHost('');
    setXhttpMode('auto');
    setXhttpHeaders([]);
    setXhttpXPaddingBytes('100-1000');
    setXhttpNoSSEHeader(false);
    setXhttpScMaxEachPostBytes('1000000');
    setXhttpScMaxBufferedPosts('30');
    setXhttpScStreamUpServerSecs('20-80');
    setMkcpMtu('1350');
    setMkcpTti('20');
    setMkcpUplinkCapacity('5');
    setMkcpDownlinkCapacity('20');
    setMkcpCongestion(false);
    setMkcpReadBufferSize('1');
    setMkcpWriteBufferSize('1');
    setMkcpHeaderType('none');
    setMkcpHeaderDomain('example.com');
    setMkcpSeed('Password');
    setGrpcAuthority('grpc.example.com');
    setGrpcServiceName('name');
    setGrpcInitialWindowsSize('0');
    setWsAcceptProxyProtocol(false);
    setWsPath('/');
    setWsHost('xray.com');
    setWsHeartbeatPeriod('0');
    setHttpUpgradeAcceptProxyProtocol(false);
    setHttpUpgradePath('/');
    setHttpUpgradeHost('xray.com');

    // 重置安全设置
    setSecurityType('none');
    setTlsRejectUnknownSni(false);
    setTlsAllowInsecure(false);
    setTlsAlpn(['h2', 'http/1.1']);
    setTlsMinVersion('1.2');
    setTlsMaxVersion('1.3');
    setTlsCertificates([]);
    setTlsDisableSystemRoot(false);
    setTlsEnableSessionResumption(false);
    setRealityTarget('example.com:443');
    setRealityXver(0);
    setRealityPrivateKey('');
    setRealityPublicKey('');
    setRealityMaxTimeDiff(0);
    setRealityShortIds(['', 'abcdef12']);
    setRealityServerNames('example.com,www.example.com');
    setRealityLimitFallbackUpload({ afterBytes: 0, bytesPerSec: 0, burstBytesPerSec: 0 });
    setRealityLimitFallbackDownload({ afterBytes: 0, bytesPerSec: 0, burstBytesPerSec: 0 });

    // 重置SockOpt设置
    setEnableSockOpt(false);
    setSockOptMark('0');
    setSockOptTcpMaxSeg('1440');
    setSockOptTcpFastOpen(false);
    setSockOptTproxy('off');
    setSockOptDomainStrategy('AsIs');
    setSockOptDialerProxy('');
    setSockOptAcceptProxyProtocol(false);
    setSockOptTcpKeepAliveInterval('0');
    setSockOptTcpKeepAliveIdle('300');
    setSockOptTcpUserTimeout('10000');
    setSockOptTcpCongestion('bbr');
    setSockOptInterface('');
    setSockOptV6Only(false);
    setSockOptTcpWindowClamp('600');
    setSockOptAddressPortStrategy('none');
  };

  // 生成传输设置
  const generateStreamSettings = () => {
    let streamSettings: any = {};

    if (transportType === 'tcp') {
      streamSettings.network = 'tcp';
      streamSettings.tcpSettings = {
        acceptProxyProtocol: tcpAcceptProxyProtocol,
        header: tcpHeaderType === 'http' ? {
          type: 'http',
          request: {
            version: tcpHttpRequestVersion,
            method: tcpHttpRequestMethod,
            path: tcpHttpPaths,
            headers: tcpHttpRequestHeaders.reduce((acc, item) => {
              if (!acc[item.key]) {
                acc[item.key] = [];
              }
              acc[item.key].push(item.value);
              return acc;
            }, {} as Record<string, string[]>)
          },
          response: {
            version: tcpHttpResponseVersion,
            status: tcpHttpResponseStatus,
            reason: tcpHttpResponseReason,
            headers: tcpHttpResponseHeaders.reduce((acc, item) => {
              if (!acc[item.key]) {
                acc[item.key] = [];
              }
              acc[item.key].push(item.value);
              return acc;
            }, {} as Record<string, string[]>)
          }
        } : {
          type: 'none'
        }
      };
    } else if (transportType === 'xhttp') {
      streamSettings.network = 'xhttp';

      // 构建headers对象
      const headers: { [key: string]: string } = {};
      xhttpHeaders.forEach(header => {
        if (header.key && header.value) {
          headers[header.key] = header.value;
        }
      });

      const xhttpSettings = {
        host: xhttpHost,
        path: xhttpPath,
        mode: xhttpMode,
        extra: {
          headers,
          xPaddingBytes: xhttpXPaddingBytes,
          noSSEHeader: xhttpNoSSEHeader,
          scMaxEachPostBytes: parseInt(xhttpScMaxEachPostBytes) || 1000000,
          scMaxBufferedPosts: parseInt(xhttpScMaxBufferedPosts) || 30,
          scStreamUpServerSecs: xhttpScStreamUpServerSecs
        }
      };

      streamSettings.xhttpSettings = mergeWith({}, (originalInbound?.streamSettings?.xhttpSettings || {}), xhttpSettings, (value,srcValue ,key) => {
        if (key === 'headers' && isObject(value)) {
            return srcValue
        }
      });
    } else if (transportType === 'kcp') {
      streamSettings.network = 'kcp';
      streamSettings.kcpSettings = {
        mtu: parseInt(mkcpMtu) || 1350,
        tti: parseInt(mkcpTti) || 20,
        uplinkCapacity: parseInt(mkcpUplinkCapacity) || 5,
        downlinkCapacity: parseInt(mkcpDownlinkCapacity) || 20,
        congestion: mkcpCongestion,
        readBufferSize: parseInt(mkcpReadBufferSize) || 1,
        writeBufferSize: parseInt(mkcpWriteBufferSize) || 1,
        header: {
          type: mkcpHeaderType,
          domain: mkcpHeaderDomain
        },
        seed: mkcpSeed
      };
    } else if (transportType === 'grpc') {
      streamSettings.network = 'grpc';
      streamSettings.grpcSettings = {...(originalInbound?.streamSettings?.grpcSettings || {}) ,...{
        authority: grpcAuthority,
        serviceName: grpcServiceName,
        initial_windows_size: parseInt(grpcInitialWindowsSize) || 0
      }};
    } else if (transportType === 'ws') {
      streamSettings.network = 'ws';
      streamSettings.wsSettings = {...(originalInbound?.streamSettings?.wsSettings || {}), ...{
        acceptProxyProtocol: wsAcceptProxyProtocol,
        path: wsPath || '/',
        host: wsHost,
        heartbeatPeriod: parseInt(wsHeartbeatPeriod) || 0
      }};
    } else if (transportType === 'httpupgrade') {
      streamSettings.network = 'httpupgrade';
      streamSettings.httpupgradeSettings = {...(originalInbound?.streamSettings?.httpupgradeSettings || {}) ,...{
        acceptProxyProtocol: httpUpgradeAcceptProxyProtocol,
        path: httpUpgradePath || '/',
        host: httpUpgradeHost
      }};
    }

    return streamSettings;
  };

  // 生成安全设置
  const generateSecuritySettings = () => {
    if (securityType === 'none') {
      return {};
    }

    let securitySettings: any = {};

    if (securityType === 'tls') {
      securitySettings.security = 'tls';
      securitySettings.tlsSettings = {...(originalInbound?.streamSettings?.tlsSettings || {}) ,...{
        rejectUnknownSni: tlsRejectUnknownSni,
        allowInsecure: tlsAllowInsecure,
        alpn: tlsAlpn,
        minVersion: tlsMinVersion,
        maxVersion: tlsMaxVersion,
        certificates: tlsCertificates,
        disableSystemRoot: tlsDisableSystemRoot,
        enableSessionResumption: tlsEnableSessionResumption
      }};
    } else if (securityType === 'reality') {
      securitySettings.security = 'reality';
      securitySettings.realitySettings = {...(originalInbound?.streamSettings?.realitySettings || {}) ,...{
        target: realityTarget,
        xver: realityXver,
        privateKey: realityPrivateKey,
        maxTimeDiff: realityMaxTimeDiff,
        shortIds: realityShortIds,
        serverNames: realityServerNames.split(',').map(name => name.trim()).filter(name => name),
        limitFallbackUpload: realityLimitFallbackUpload,
        limitFallbackDownload: realityLimitFallbackDownload,
        settings: {
          publicKey: realityPublicKey
        }
      }};
    }

    return securitySettings;
  };

  // 生成SockOpt设置
  const generateSockOptSettings = () => {
    if (!enableSockOpt) {
      return {};
    }

    const sockOpt: any = originalInbound?.streamSettings?.sockopt || {};

    // Mark
    if (sockOptMark) {
      const mark = parseInt(sockOptMark);
      if (!isNaN(mark)) {
        sockOpt.mark = mark;
      }
    }

    // TCP Max Seg
    if (sockOptTcpMaxSeg) {
      const tcpMaxSeg = parseInt(sockOptTcpMaxSeg);
      if (!isNaN(tcpMaxSeg)) {
        sockOpt.tcpMaxSeg = tcpMaxSeg;
      }
    }

    // TCP Fast Open
    sockOpt.tcpFastOpen = sockOptTcpFastOpen

    // TProxy
    if (sockOptTproxy && sockOptTproxy !== 'off') {
      sockOpt.tproxy = sockOptTproxy;
    }

    // Domain Strategy
    if (sockOptDomainStrategy !== 'AsIs') {
      sockOpt.domainStrategy = sockOptDomainStrategy;
    }

    // Dialer Proxy
    if (sockOptDialerProxy.trim()) {
      sockOpt.dialerProxy = sockOptDialerProxy.trim();
    }

    // Accept Proxy Protocol
    sockOpt.acceptProxyProtocol = sockOptAcceptProxyProtocol

    // TCP Keep Alive Interval
    if (sockOptTcpKeepAliveInterval) {
      const interval = parseInt(sockOptTcpKeepAliveInterval);
      if (!isNaN(interval) && interval > 0) {
        sockOpt.tcpKeepAliveInterval = interval;
      }
    }

    // TCP Keep Alive Idle
    if (sockOptTcpKeepAliveIdle) {
      const idle = parseInt(sockOptTcpKeepAliveIdle);
      if (!isNaN(idle)) {
        sockOpt.tcpKeepAliveIdle = idle;
      }
    }

    // TCP User Timeout
    if (sockOptTcpUserTimeout) {
      const timeout = parseInt(sockOptTcpUserTimeout);
      if (!isNaN(timeout)) {
        sockOpt.tcpUserTimeout = timeout;
      }
    }

    // TCP Congestion
    if (sockOptTcpCongestion && sockOptTcpCongestion !== 'bbr') {
      sockOpt.tcpcongestion = sockOptTcpCongestion;
    }

    // Interface
    if (sockOptInterface.trim()) {
      sockOpt.interface = sockOptInterface.trim();
    }

    // V6 Only
    sockOpt.V6Only = sockOptV6Only

    // TCP Window Clamp
    if (sockOptTcpWindowClamp) {
      const clamp = parseInt(sockOptTcpWindowClamp);
      if (!isNaN(clamp)) {
        sockOpt.tcpWindowClamp = clamp;
      }
    }

    // Address Port Strategy
    if (sockOptAddressPortStrategy && sockOptAddressPortStrategy !== 'none') {
      sockOpt.addressPortStrategy = sockOptAddressPortStrategy;
    }

    return Object.keys(sockOpt).length > 0 ? { sockopt: sockOpt } : {};
  };

  const handleSave = async () => {
    // 防止重复点击
    if (isSaving) {
      return;
    }

    setIsSaving(true);

    try {
      // 验证必填字段
      if (!protocol) {
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorProtocolRequired'));
        return;
      }
      if (!port) {
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorPortRequired'));
        return;
      }

      const portNum = parseInt(port);
      if (isNaN(portNum) || portNum < 1 || portNum > 65535) {
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorPortInvalid'));
        return;
      }

    // 生成协议特定的设置
    let settings: any = {};

    if (protocol === Protocols.VLESS) {
      settings = {
        clients: users,
        decryption: 'none',
        fallbacks: fallbacks
      };
    } else if (protocol === Protocols.VMESS) {
      settings = {
        clients: users
      };
    } else if (protocol === Protocols.TROJAN) {
      settings = {
        clients: users,
        fallbacks: fallbacks
      };
    } else if (protocol === Protocols.SHADOWSOCKS) {
      settings = {
        network: ssNetwork,
        method: ssMethod,
        password: ssPassword || generateRandomBase64Key(getPasswordBytesForMethod(ssMethod)),
        clients: users
      };
    } else if (protocol === Protocols.HTTP) {
      settings = {
        accounts: httpUseAuth ? [{ user: httpUsername, pass: httpPassword }] : [],
        allowTransparent
      };
    } else if (protocol === Protocols.SOCKS) {
      settings = {
        auth: socksUseAuth ? 'password' : 'noauth',
        accounts: socksUseAuth ? [{ user: socksUsername, pass: socksPassword }] : [],
        udp: socksUdp,
        ip: socksIp,
        userLevel: 0
      };
    } else if (protocol === Protocols.DOKODEMO) {
      const dokoPortNum = parseInt(dokoPort);
      settings = {
        address: dokoAddress,
        port: isNaN(dokoPortNum) ? 53 : dokoPortNum,
        network: dokoNetwork,
        followRedirect
      };
    } else if (protocol === Protocols.WIREGUARD) {
      settings = {
        secretKey: wgSecretKey || 'PRIVATE_KEY',
        publicKey: wgPublicKey || 'PUBLIC_KEY',
        peers: wgPeers,
        mtu: parseInt(wgMtu) || 1420
      };
    }
    settings = {...(originalInbound?.settings || {}), ...settings}
    // 生成传输设置（仅对支持的协议）
    let streamSettings = ['vmess', 'vless', 'trojan', 'shadowsocks'].includes(protocol)
      ? generateStreamSettings()
      : {};

    // 合并安全设置和SockOpt设置
    if (['vmess', 'vless', 'trojan', 'shadowsocks'].includes(protocol)) {
      const securitySettings = generateSecuritySettings();
      const sockOptSettings = generateSockOptSettings();
      streamSettings = { ...streamSettings, ...securitySettings, ...sockOptSettings };
    }

    // 创建入站配置对象
    const newInbound: InboundConfig = {
      enable: originalInbound?.enable || true,
      listen: listen.trim() || '0.0.0.0',
      port: portNum,
      protocol,
      settings,
      ...(streamSettings && { streamSettings }),
      tag: `${protocol}-${portNum}`,
      sniffing: {
        enabled: enableSniffing,
        destOverride: sniffingDestOverride,
        metadataOnly: sniffingMetadataOnly,
        domainsExcluded: sniffingDomainsExcluded,
        routeOnly: sniffingRouteOnly
      },
      allocate: {
        strategy: 'always',
        refresh: 5,
        concurrency: 3
      }
    };

    // 如果是添加模式则设置enable为true，编辑模式不设置enable
    if (!isEditMode) {
      (newInbound as any).enable = true;
    }

    // 添加可选字段
    if (expiryTime.trim()) {
      const days = parseInt(expiryTime);
      if (!isNaN(days)) {
        if (days === 0) {
          // 如果输入0，保存时设为1（已过期的时间戳）
          newInbound.expiryTime = 1;
        } else if (days > 0) {
          // 设为当前时间加上有效期的时间戳（毫秒）
          const currentTime = Date.now();
          const expiryTimestamp = currentTime + (days * 24 * 60 * 60 * 1000);
          newInbound.expiryTime = expiryTimestamp;
        } else {
          // 如果输入负数，不设置过期时间（永久有效）
          newInbound.expiryTime = 0;
        }
      } else {
        // 如果输入无效数字，不设置过期时间（永久有效）
        newInbound.expiryTime = 0;
      }
    } else {
      // 留空则不设置过期时间（永久有效）
      newInbound.expiryTime = 0;
    }

    if (totalTraffic.trim()) {
      const traffic = parseFloat(totalTraffic);
      if (!isNaN(traffic) && traffic > 0) {
        // 转换为字节：GB * 1024 * 1024 * 1024
        newInbound.total = traffic * 1024 * 1024 * 1024;
      }
    } else {
      // 留空则设为0表示不限流量
      newInbound.total = 0;
    }

      try {
        if (isEditMode && id) {
          // 编辑模式：使用API更新配置
          if (!originalInbound) {
            Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorOriginalConfigNotFound'));
            return;
          }

          // 准备表单数据
          const formData = new FormData();

          // 遍历合并后的配置对象，添加到表单
          Object.entries(newInbound).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              if (typeof value === 'object') {
                formData.append(key, JSON.stringify(value));
              } else {
                formData.append(key, String(value));
              }
            }
          });

          // 调用API更新配置
          const config = configs.find(c => c.id === configId);
          if (!config) {
            Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorServerConfigNotFound'));
            return;
          }

          const response = await smartFetch(
            `${config.protocol}://${config.url}/panel/inbound/update/${originalInbound.id}`,
            {
              method: 'POST',
              body: formData,
            },
            config
          );

          const result = await response.json();

          if (result.success) {
            // 调用获取inbounds列表函数
            await getThreeXUIInboundList(config as ThreeXUIConfig);

            // 编辑模式下保存成功后发送重启请求（不等待响应）
            const restartUrl = `${config.protocol}://${config.url}/server/restartXrayService`;
            smartFetch(restartUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              credentials: 'include'
            }, config).catch(error => {
              console.log('Auto restart request failed:', error);
            });

            Alert.alert(t('common.success'), t('threeXUI.inboundConfig.successConfigUpdated'), [
              { text: t('common.ok'), onPress: () => router.back() }
            ]);
          } else {
            Alert.alert(t('common.error'), result.msg || t('threeXUI.inboundConfig.errorSaveFailed'));
          }
        } else {
          // 添加模式：生成新配置对象
          const addInbound = {
            ...newInbound,
            tag: `${protocol}-${port}`, // 自动生成tag为协议-端口格式
            up: 0,
            down: 0,
            enable: true
          };

          // 准备表单数据
          const formData = new FormData();

          // 遍历配置对象，添加到表单
          Object.entries(addInbound).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              if (typeof value === 'object') {
                formData.append(key, JSON.stringify(value));
              } else {
                formData.append(key, String(value));
              }
            }
          });

          // 调用API添加配置
          const config = configs.find(c => c.id === configId);
          if (!config) {
            Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorServerConfigNotFound'));
            return;
          }

          const response = await smartFetch(
            `${config.protocol}://${config.url}/panel/inbound/add`,
            {
              method: 'POST',
              body: formData,
            },
            config
          );

          const result = await response.json();

          if (result.success) {
            // 调用获取inbounds列表函数
            await getThreeXUIInboundList(config as ThreeXUIConfig);

            Alert.alert(t('common.success'), t('threeXUI.inboundConfig.successConfigAdded'), [
              { text: t('common.ok'), onPress: () => router.back() }
            ]);
          } else {
            Alert.alert(t('common.error'), result.msg || t('threeXUI.inboundConfig.errorSaveFailed'));
          }
        }
      } catch (error) {
        console.error('保存配置失败:', error);
        Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorSaveFailed'));
      }
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <>
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <KeyboardAwareScrollView style={styles.scrollView} contentContainerStyle={styles.content} bottomOffset={50}>
        {/* 协议选择 */}
        <View style={styles.section}>
          <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.protocol')}</Label>
          <Select value={{ value: protocol, label: protocol }} onValueChange={handleProtocolChange}>
            <SelectTrigger style={[styles.input,{borderColor}]} className='w-[250px]'>
              <SelectValue className='text-foreground text-sm native:text-lg' placeholder={t('threeXUI.inboundConfig.protocolPlaceholder')} />
            </SelectTrigger>
            {!isEditMode && (
              <SelectContent className='w-[250px]'>
                <SelectItem value={Protocols.VMESS} label="VMess">VMess</SelectItem>
                <SelectItem value={Protocols.VLESS} label="VLESS">VLESS</SelectItem>
                <SelectItem value={Protocols.TROJAN} label="Trojan">Trojan</SelectItem>
                <SelectItem value={Protocols.SHADOWSOCKS} label="Shadowsocks">Shadowsocks</SelectItem>
                <SelectItem value={Protocols.DOKODEMO} label="Dokodemo-door">Dokodemo-door</SelectItem>
                <SelectItem value={Protocols.SOCKS} label="SOCKS">SOCKS</SelectItem>
                <SelectItem value={Protocols.HTTP} label="HTTP">HTTP</SelectItem>
                <SelectItem value={Protocols.WIREGUARD} label="WireGuard">WireGuard</SelectItem>
              </SelectContent>
            )}
          </Select>
        </View>

        {/* 监听和端口 */}
        <View style={styles.row}>
          <View style={[styles.section, styles.flex1]}>
            <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.listen')}</Label>
            <Input
              style={[styles.input, { borderColor }]}
              value={listen}
              onChangeText={setListen}
              placeholder={t('threeXUI.inboundConfig.listenPlaceholder')}
            />
          </View>
          <View style={[styles.section, styles.flex1, styles.marginLeft]}>
            <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.port')}</Label>
            <Input
              style={[styles.input, { borderColor }]}
              value={port}
              onChangeText={setPort}
              placeholder={t('threeXUI.inboundConfig.portPlaceholder')}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* 有效期和总流量 */}
        <View style={styles.row}>
          <View style={[styles.section, styles.flex1]}>
            <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.expiryTime')}</Label>
            <Input
              style={[styles.input, { borderColor }]}
              value={expiryTime}
              onChangeText={setExpiryTime}
              placeholder={t('threeXUI.inboundConfig.expiryTimePlaceholder')}
              keyboardType="numeric"
            />
          </View>
          <View style={[styles.section, styles.flex1, styles.marginLeft]}>
            <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.totalTraffic')}</Label>
            <Input
              style={[styles.input, { borderColor }]}
              value={totalTraffic}
              onChangeText={setTotalTraffic}
              placeholder={t('threeXUI.inboundConfig.totalTrafficPlaceholder')}
              keyboardType="numeric"
            />
          </View>
        </View>



        {/* 协议特定设置 */}
        {protocol && ['vmess', 'vless', 'trojan', 'shadowsocks'].includes(protocol) && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.userManagement')}</Text>
              <Button onPress={handleAddUser} size="sm" variant="secondary">
                <Text style={[styles.editButtonText, { color: textColor }]}>{t('threeXUI.inboundConfig.addUser')}</Text>
              </Button>
            </View>

            {users.length === 0 ? (
              <Text style={[styles.placeholder, { color: textColor + '80' }]}>
                {t('threeXUI.inboundConfig.noUsers')}
              </Text>
            ) : (
              <View style={styles.userList}>
                {users.map((user, index) => (
                  <View key={index} style={[styles.userCard, { borderColor }]}>
                    <View style={styles.userInfo}>
                      <Text style={[styles.userEmail, { color: textColor }]}>{user.email}</Text>
                    </View>
                    <View style={styles.userActions}>
                      <TouchableOpacity onPress={() => handleEditUser(user)} style={styles.actionButton}>
                        <Edit size={16} color={textColor} />
                      </TouchableOpacity>
                      <TouchableOpacity onPress={() => handleDeleteUser(user)} style={styles.actionButton}>
                        <Trash2 size={16} color="#ef4444" />
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}

        {/* 回落管理 */}
        {protocol && ['vless', 'trojan'].includes(protocol) && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.fallbackManagement')}</Text>
              <Button onPress={handleAddFallback} size="sm" variant="secondary">
                <Text style={[styles.editButtonText, { color: textColor }]}>{t('threeXUI.inboundConfig.addFallback')}</Text>
              </Button>
            </View>

            {fallbacks.length === 0 ? (
              <Text style={[styles.placeholder, { color: textColor + '80' }]}>
                {t('threeXUI.inboundConfig.noFallbacks')}
              </Text>
            ) : (
              <View style={styles.userList}>
                {fallbacks.map((fallback, index) => (
                  <View key={index} style={[styles.userCard, { borderColor }]}>
                    <View style={styles.userInfo}>
                      <Text style={[styles.userEmail, { color: textColor }]}>
                        {fallback.dest}:{fallback.path || '/'}
                      </Text>
                      <Text style={[styles.userEmail, { color: textColor + '80', fontSize: 12 }]}>
                        {fallback.name || `${t('threeXUI.inboundConfig.fallbackDefaultName')}-${index + 1}`} {fallback.alpn ? `(${fallback.alpn})` : ''}
                      </Text>
                    </View>
                    <View style={styles.userActions}>
                      <TouchableOpacity onPress={() => handleEditFallback(fallback, index)} style={styles.actionButton}>
                        <Edit size={16} color={textColor} />
                      </TouchableOpacity>
                      <TouchableOpacity onPress={() => handleDeleteFallback(index)} style={styles.actionButton}>
                        <Trash2 size={16} color="#ef4444" />
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}

        {/* Shadowsocks 设置 */}
        {protocol === Protocols.SHADOWSOCKS && (
          <View>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.shadowsocksSettings')}</Text>
            {/* 加密方法独占一行 */}
            <View style={styles.section}>
              <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.encryptionMethod')}</Label>
              <Select value={{ value: ssMethod, label: ssMethod }} onValueChange={handleSsMethodChange}>
                <SelectTrigger style={[styles.input, { borderColor }]}>
                  <SelectValue className='text-foreground text-sm native:text-lg' placeholder={t('threeXUI.inboundConfig.encryptionMethodPlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="aes-256-gcm" label="aes-256-gcm">aes-256-gcm</SelectItem>
                  <SelectItem value="aes-128-gcm" label="aes-128-gcm">aes-128-gcm</SelectItem>
                  <SelectItem value="chacha20-poly1305" label="chacha20-poly1305">chacha20-poly1305</SelectItem>
                  <SelectItem value="xchacha20-poly1305" label="xchacha20-poly1305">xchacha20-poly1305</SelectItem>
                  <SelectItem value="2022-blake3-aes-128-gcm" label="2022-blake3-aes-128-gcm">2022-blake3-aes-128-gcm</SelectItem>
                  <SelectItem value="2022-blake3-aes-256-gcm" label="2022-blake3-aes-256-gcm">2022-blake3-aes-256-gcm</SelectItem>
                  <SelectItem value="2022-blake3-chacha20-poly1305" label="2022-blake3-chacha20-poly1305">2022-blake3-chacha20-poly1305</SelectItem>
                </SelectContent>
              </Select>
            </View>

            {/* 网络类型与密码在一行 */}
            <View style={styles.row}>
              <View style={[styles.section,styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.networkType')}</Label>
                <Select value={{ value: ssNetwork, label: ssNetwork }} onValueChange={(option) => setSsNetwork((option?.value || option) as string)}>
                  <SelectTrigger style={[styles.input, { borderColor }]}>
                    <SelectValue className='text-foreground text-sm native:text-lg' placeholder={t('threeXUI.inboundConfig.networkTypePlaceholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tcp,udp" label="TCP+UDP">TCP+UDP</SelectItem>
                    <SelectItem value="tcp" label="TCP">TCP</SelectItem>
                    <SelectItem value="udp" label="UDP">UDP</SelectItem>
                  </SelectContent>
                </Select>
              </View>
              <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                <View style={styles.labelWithButton}>
                  <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.password')}</Label>
                  <TouchableOpacity onPress={() => handleGenerateSSPassword()}>
                    <RefreshCcw size={16} color={textColor} />
                  </TouchableOpacity>
                </View>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={ssPassword}
                  onChangeText={setSsPassword}
                  placeholder={t('threeXUI.inboundConfig.passwordPlaceholder')}
                />
              </View>
            </View>
          </View>
        )}

        {/* HTTP 设置 */}
        {protocol === Protocols.HTTP && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.httpSettings')}</Text>

            <View style={styles.section}>
              <View style={styles.switchRowCompact}>
                <Switch
                  checked={httpUseAuth}
                  onCheckedChange={setHttpUseAuth}
                />
                <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.useAuth')}</Label>
              </View>
            </View>

            {httpUseAuth && (
              <View style={styles.row}>
                <View style={[styles.section, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.username')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={httpUsername}
                    onChangeText={setHttpUsername}
                    placeholder={t('threeXUI.inboundConfig.usernamePlaceholder')}
                  />
                </View>
                <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.password')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={httpPassword}
                    onChangeText={setHttpPassword}
                    placeholder={t('threeXUI.inboundConfig.passwordPlaceholder')}
                  />
                </View>
              </View>
            )}

            <View >
              <View style={styles.switchRowCompact}>
                <Switch
                  checked={allowTransparent}
                  onCheckedChange={setAllowTransparent}
                />
                <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.allowTransparent')}</Label>
              </View>
            </View>
          </View>
        )}

        {/* SOCKS 设置 */}
        {protocol === Protocols.SOCKS && (
          <View>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.socksSettings')}</Text>

            <View style={styles.section}>
              <View style={styles.switchRowCompact}>
                <Switch
                  checked={socksUseAuth}
                  onCheckedChange={setSocksUseAuth}
                />
                <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.useAuth')}</Label>
              </View>
            </View>

            {socksUseAuth && (
              <View style={styles.row}>
                <View style={[styles.section, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.username')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={socksUsername}
                    onChangeText={setSocksUsername}
                    placeholder={t('threeXUI.inboundConfig.usernamePlaceholder')}
                  />
                </View>
                <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.password')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={socksPassword}
                    onChangeText={setSocksPassword}
                    placeholder={t('threeXUI.inboundConfig.passwordPlaceholder')}
                  />
                </View>
              </View>
            )}

            <View style={styles.section}>
              <View style={styles.switchRowCompact}>
                <Switch
                  checked={socksUdp}
                  onCheckedChange={setSocksUdp}
                />
                <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.enableUdp')}</Label>
              </View>
            </View>

            {socksUdp && (
              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.ipAddress')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={socksIp}
                  onChangeText={setSocksIp}
                  placeholder="127.0.0.1"
                />
              </View>
            )}
          </View>
        )}

        {/* Dokodemo-door 设置 */}
        {protocol === Protocols.DOKODEMO && (
          <View>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.dokodemoSettings')}</Text>

            <View style={styles.row}>
              <View style={[styles.section, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.targetAddress')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={dokoAddress}
                  onChangeText={setDokoAddress}
                  placeholder={t('threeXUI.inboundConfig.targetAddressPlaceholder')}
                />
              </View>
              <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.targetPort')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={dokoPort}
                  onChangeText={setDokoPort}
                  placeholder={t('threeXUI.inboundConfig.targetPortPlaceholder')}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.row}>
              <View style={[styles.section, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.networkType')}</Label>
                <Select value={{ value: dokoNetwork, label: dokoNetwork === 'tcp,udp' ? 'TCP+UDP' : dokoNetwork.toUpperCase() }} onValueChange={(option) => setDokoNetwork((option?.value || option) as string)}>
                  <SelectTrigger style={[styles.input, { borderColor }]}>
                    <SelectValue className='text-foreground text-sm native:text-lg' placeholder={t('threeXUI.inboundConfig.networkTypePlaceholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tcp" label="TCP">TCP</SelectItem>
                    <SelectItem value="udp" label="UDP">UDP</SelectItem>
                    <SelectItem value="tcp,udp" label="TCP+UDP">TCP+UDP</SelectItem>
                  </SelectContent>
                </Select>
              </View>
              <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={followRedirect}
                    onCheckedChange={setFollowRedirect}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.followRedirect')}</Label>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* WireGuard 设置 */}
        {protocol === Protocols.WIREGUARD && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.wireguardSettings')}</Text>

            <View style={styles.row}>
              <View style={[styles.section, styles.flex1]}>
                <View style={styles.labelWithButton}>
                  <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.privateKey')}</Label>
                  <TouchableOpacity onPress={async () => {
                    try {
                      const keys = await generateWireguardKeys();
                      setWgSecretKey(keys.privateKey);
                      setWgPublicKey(keys.publicKey);
                    } catch (error) {
                      Alert.alert(t('common.error'), t('threeXUI.inboundConfig.errorGenerateKeysFailed'));
                    }
                  }}>
                    <RefreshCcw size={16} color={textColor} />
                  </TouchableOpacity>
                </View>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={wgSecretKey}
                  onChangeText={setWgSecretKey}
                  placeholder={t('threeXUI.inboundConfig.privateKeyPlaceholder')}
                  secureTextEntry
                />
              </View>
              <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.mtu')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={wgMtu}
                  onChangeText={setWgMtu}
                  placeholder={t('threeXUI.inboundConfig.mtuPlaceholder')}
                  keyboardType="numeric"
                />
              </View>
            </View>

            {/* 公钥显示 */}
            <View style={styles.section}>
              <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.publicKey')}</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={wgPublicKey}
                placeholder={t('threeXUI.inboundConfig.publicKeyPlaceholder')}
                editable={false}
              />
            </View>

            {/* Peer 管理 */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.peerManagement')}</Text>
                <Button onPress={handleAddPeer} size="sm" variant="secondary">
                  <Text style={[styles.editButtonText, { color: textColor }]}>{t('threeXUI.inboundConfig.addPeer')}</Text>
                </Button>
              </View>

              {wgPeers.length === 0 ? (
                <Text style={[styles.placeholder, { color: textColor + '80' }]}>
                  {t('threeXUI.inboundConfig.noPeers')}
                </Text>
              ) : (
                <View style={styles.userList}>
                  {wgPeers.map((peer, index) => (
                    <View key={index} style={[styles.userCard, { borderColor }]}>
                      <View style={styles.userInfo}>
                        <Text style={[styles.userEmail, { color: textColor }]}>
                          Peer {index + 1}
                        </Text>
                        <Text style={[styles.userEmail, { color: textColor + '80', fontSize: 12 }]}>
                          {peer.publicKey?.substring(0, 20)}...
                        </Text>
                        <Text style={[styles.userEmail, { color: textColor + '80', fontSize: 12 }]}>
                          {peer.allowedIPs?.join(', ') || '0.0.0.0/0, ::/0'}
                        </Text>
                      </View>
                      <View style={styles.userActions}>
                        <TouchableOpacity onPress={() => handleEditPeer(peer, index)} style={styles.actionButton}>
                          <Edit size={16} color={textColor} />
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => handleDeletePeer(index)} style={styles.actionButton}>
                          <Trash2 size={16} color="#ef4444" />
                        </TouchableOpacity>
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </View>
          </View>
        )}

        {/* 传输设置 */}
        {protocol && ['vmess', 'vless', 'trojan', 'shadowsocks'].includes(protocol) && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.transportSettings')}</Text>
              <Button onPress={() => transportBottomSheetRef.current?.present()} size="sm" variant="secondary">
                <Text style={[styles.editButtonText, { color: textColor }]}>{t('threeXUI.inboundConfig.edit')}</Text>
              </Button>
            </View>

            <View style={[styles.transportInfo, { borderColor }]}>
              <Text style={[styles.transportType, { color: textColor }]}>
                {t('threeXUI.inboundConfig.transportType')}: {transportType.toUpperCase()}
              </Text>
              {transportType === 'tcp' && tcpHeaderType !== 'none' && (
                <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                  {t('threeXUI.inboundConfig.tcpHeaderType')}: {tcpHeaderType}
                </Text>
              )}
              {transportType === 'xhttp' && (
                <>
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    {t('threeXUI.inboundConfig.hostLabel')}: {xhttpHost || t('threeXUI.inboundConfig.notSet')}, {t('threeXUI.inboundConfig.pathLabel')}: {xhttpPath}
                  </Text>
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    {t('threeXUI.inboundConfig.modeLabel')}: {xhttpMode}, {t('threeXUI.inboundConfig.headersCount')}: {xhttpHeaders.length}{t('threeXUI.inboundConfig.headersCountSuffix')}
                  </Text>
                </>
              )}
              {transportType === 'kcp' && (
                <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                  MTU: {mkcpMtu}, TTI: {mkcpTti}
                </Text>
              )}
              {transportType === 'grpc' && grpcServiceName && (
                <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                  {t('threeXUI.inboundConfig.serviceName')}: {grpcServiceName}
                </Text>
              )}
              {transportType === 'ws' && (
                <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                  {t('threeXUI.inboundConfig.pathLabel')}: {wsPath}
                </Text>
              )}
              {transportType === 'httpupgrade' && (
                <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                  {t('threeXUI.inboundConfig.pathLabel')}: {httpUpgradePath}
                </Text>
              )}
            </View>
          </View>
        )}

        {/* 安全设置 */}
        {protocol && ['vmess', 'vless', 'trojan', 'shadowsocks'].includes(protocol) && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.securitySettings')}</Text>
              <Button onPress={() => securityBottomSheetRef.current?.present()} size="sm" variant="secondary">
                <Text style={[styles.editButtonText, { color: textColor }]}>{t('threeXUI.inboundConfig.edit')}</Text>
              </Button>
            </View>

            <View style={[styles.transportInfo, { borderColor }]}>
              <Text style={[styles.transportType, { color: textColor }]}>
                {t('threeXUI.inboundConfig.securityType')}: {securityType.toUpperCase()}
              </Text>
              {securityType === 'tls' && tlsCertificates.length > 0 && (
                <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                  {t('threeXUI.inboundConfig.certificateCount')}: {tlsCertificates.length}{t('threeXUI.inboundConfig.certificateCountSuffix')}
                </Text>
              )}
              {securityType === 'reality' && realityTarget && (
                <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                  {t('threeXUI.inboundConfig.target')}: {realityTarget}
                </Text>
              )}
            </View>
          </View>
        )}

        {/* SockOpt设置 */}
        {protocol && ['vmess', 'vless', 'trojan', 'shadowsocks'].includes(protocol) && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.sockOptSettings')}</Text>
              <View style={styles.sockOptControls}>
                <Switch
                  checked={enableSockOpt}
                  onCheckedChange={setEnableSockOpt}
                />
                <Button
                  onPress={() => sockOptBottomSheetRef.current?.present()}
                  size="sm"
                  variant="secondary"
                  disabled={!enableSockOpt}
                  style={[!enableSockOpt && styles.disabledButton]}
                >
                  <Text style={[styles.editButtonText, { color: enableSockOpt ? textColor : textColor + '50' }]}>{t('threeXUI.inboundConfig.edit')}</Text>
                </Button>
              </View>
            </View>

            {enableSockOpt ? (
              <View style={[styles.transportInfo, { borderColor }]}>
                <Text style={[styles.transportType, { color: textColor }]}>
                  {t('threeXUI.inboundConfig.sockOptEnabled')}
                </Text>
                {sockOptMark && (
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    Mark: {sockOptMark}
                  </Text>
                )}
                {sockOptTcpFastOpen && (
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    {t('threeXUI.inboundConfig.tcpFastOpenEnabled')}
                  </Text>
                )}
                {sockOptTproxy && (
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    TProxy: {sockOptTproxy}
                  </Text>
                )}
                {sockOptDomainStrategy !== 'AsIs' && (
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    {t('threeXUI.inboundConfig.sockOptDomainStrategy')}: {sockOptDomainStrategy}
                  </Text>
                )}
              </View>
            ) : (
              <Text style={[styles.placeholder, { color: textColor + '80' }]}>
                {t('threeXUI.inboundConfig.sockOptDisabled')}
              </Text>
            )}
          </View>
        )}

        {/* Sniffing 设置 */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.sniffingSettings')}</Text>
            <View style={styles.sockOptControls}>
              <Switch
                checked={enableSniffing}
                onCheckedChange={setEnableSniffing}
              />
              <Button
                onPress={() => sniffingBottomSheetRef.current?.present()}
                size="sm"
                variant="secondary"
                disabled={!enableSniffing}
                style={[!enableSniffing && styles.disabledButton]}
              >
                <Text style={[styles.editButtonText, { color: enableSniffing ? textColor : textColor + '50' }]}>{t('threeXUI.inboundConfig.edit')}</Text>
              </Button>
            </View>
          </View>

          {enableSniffing ? (
            <View style={[styles.transportInfo, { borderColor }]}>
              <Text style={[styles.transportType, { color: textColor }]}>
                {t('threeXUI.inboundConfig.sniffingEnabled')}
              </Text>
              <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                {t('threeXUI.inboundConfig.destOverride')}: {sniffingDestOverride.join(', ')}
              </Text>
              <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                {t('threeXUI.inboundConfig.metadataOnly')}: {sniffingMetadataOnly ? t('threeXUI.inboundConfig.yes') : t('threeXUI.inboundConfig.no')}
              </Text>
              <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                {t('threeXUI.inboundConfig.routeOnly')}: {sniffingRouteOnly ? t('threeXUI.inboundConfig.yes') : t('threeXUI.inboundConfig.no')}
              </Text>
              {sniffingDomainsExcluded.length > 0 && (
                <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                  {t('threeXUI.inboundConfig.excludedDomains')}: {sniffingDomainsExcluded.join(', ')}
                </Text>
              )}
            </View>
          ) : (
            <Text style={[styles.placeholder, { color: textColor + '80' }]}>
              {t('threeXUI.inboundConfig.sniffingDisabled')}
            </Text>
          )}
        </View>
      </KeyboardAwareScrollView>

      {/* 用户编辑对话框 */}
      <Modal
        visible={showUserDialog}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowUserDialog(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor }]}>
            {/* 标题 */}
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: textColor }]}>
                {editingUser ? t('threeXUI.inboundConfig.editTitle') : t('threeXUI.inboundConfig.addUser')}
              </Text>
            </View>

            <KeyboardAwareScrollView style={styles.modalBody} showsVerticalScrollIndicator={false} bottomOffset={50}>
              {/* 邮箱 */}
              <View style={styles.dialogSection}>
                <View style={styles.labelWithButton}>
                  <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.email')}</Label>
                  <TouchableOpacity onPress={() => setUserEmail(Math.random().toString(36).substring(2, 14))}>
                    <RefreshCcw size={16} color={textColor} />
                  </TouchableOpacity>
                </View>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={userEmail}
                  onChangeText={setUserEmail}
                  placeholder={t('threeXUI.inboundConfig.emailPlaceholder')}
                />
              </View>

              {/* UUID (VLESS/VMess) */}
              {(protocol === Protocols.VLESS || protocol === Protocols.VMESS) && (
                <View style={styles.dialogSection}>
                  <View style={styles.labelWithButton}>
                    <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.uuid')}</Label>
                    <TouchableOpacity onPress={() => setUserId(Crypto.randomUUID())}>
                      <RefreshCcw size={16} color={textColor} />
                    </TouchableOpacity>
                  </View>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={userId}
                    onChangeText={setUserId}
                    placeholder={t('threeXUI.inboundConfig.uuidPlaceholder')}
                  />
                </View>
              )}

              {/* 密码 (Trojan & Shadowsocks) */}
              {(protocol === Protocols.TROJAN || protocol === Protocols.SHADOWSOCKS) && (
                <View style={styles.dialogSection}>
                  {protocol === Protocols.SHADOWSOCKS ? (
                    <>
                      <View style={styles.labelWithButton}>
                        <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.password')}</Label>
                        <TouchableOpacity onPress={handleGenerateSSUserPassword}>
                          <RefreshCcw size={16} color={textColor} />
                        </TouchableOpacity>
                      </View>
                      <Input
                        style={[styles.input, { borderColor }]}
                        value={userPassword}
                        onChangeText={setUserPassword}
                        placeholder={t('threeXUI.inboundConfig.passwordPlaceholder')}
                      />
                    </>
                  ) : (
                    <>
                      <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.password')}</Label>
                      <Input
                        style={[styles.input, { borderColor }]}
                        value={userPassword}
                        onChangeText={setUserPassword}
                        placeholder={t('threeXUI.inboundConfig.passwordPlaceholder')}
                      />
                    </>
                  )}
                </View>
              )}

              {/* XTLS Flow (VLESS) */}
              {(protocol === Protocols.VLESS && securityType === 'reality') && (
                <View style={styles.dialogSection}>
                  <View style={styles.xtlsFlowRow}>
                    <Switch
                      checked={enableXtlsFlow}
                      onCheckedChange={(checked) => {
                        setEnableXtlsFlow(checked);
                        if (checked) {
                          setUserFlow('xtls-rprx-vision');
                        } else {
                          setUserFlow('');
                        }
                      }}
                    />
                    <Label style={[styles.xtlsFlowLabel, { color: textColor }]}>{t('threeXUI.inboundConfig.enableXtlsFlow')}</Label>
                  </View>
                </View>
              )}
            </KeyboardAwareScrollView>

            <View style={styles.modalButtons}>
              <Button variant="outline" onPress={() => setShowUserDialog(false)}>
                <Text>{t('threeXUI.inboundConfig.cancel')}</Text>
              </Button>
              <Button onPress={handleSaveUser}>
                <Text>{t('threeXUI.inboundConfig.save')}</Text>
              </Button>
            </View>
          </View>
        </View>
      </Modal>

      {/* 传输设置底部弹窗 */}
      <BottomSheetModal
        ref={transportBottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
        containerComponent={WindowOverlay}
      >
        <BottomSheetKeyboardAwareScrollView style={[styles.bottomSheetContent, { backgroundColor }]} bottomOffset={50}>
          <Text style={[styles.bottomSheetTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.transportSettings')}</Text>

          {/* 传输方式选择 */}
          <View style={styles.section}>
            <Label style={[styles.label,{ color: textColor }]}>{t('threeXUI.inboundConfig.transportType')}</Label>
            <Select value={{ value: transportType, label: transportType.toUpperCase() }} onValueChange={(option) => setTransportType((option?.value || option) as string)}>
              <SelectTrigger className='w-[250px]' style={[styles.input, { borderColor }]}>
                <SelectValue className='text-foreground text-sm native:text-lg' placeholder={t('threeXUI.inboundConfig.tcpHeaderTypePlaceholder')} />
              </SelectTrigger>
              <SelectContent className='w-[250px]'>
                <SelectItem value="tcp" label="TCP (Raw)">TCP (Raw)</SelectItem>
                <SelectItem value="xhttp" label="XHTTP">XHTTP</SelectItem>
                <SelectItem value="kcp" label="KCP">KCP</SelectItem>
                <SelectItem value="grpc" label="gRPC">gRPC</SelectItem>
                <SelectItem value="ws" label="WebSocket">WebSocket</SelectItem>
                <SelectItem value="httpupgrade" label="HTTP Upgrade">HTTP Upgrade</SelectItem>
              </SelectContent>
            </Select>
          </View>

          {/* TCP 设置 */}
          {transportType === 'tcp' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.tcpSettings')}</Text>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={tcpAcceptProxyProtocol}
                    onCheckedChange={setTcpAcceptProxyProtocol}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.acceptProxyProtocol')}</Label>
                </View>
              </View>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={tcpHeaderType === 'http'}
                    onCheckedChange={(checked) => setTcpHeaderType(checked ? 'http' : 'none')}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.httpMasquerading')}</Label>
                </View>
              </View>

              {/* HTTP 伪装设置 */}
              {tcpHeaderType === 'http' && (
                <View style={styles.section}>
                  <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.httpMasqueradingSettings')}</Text>

                  {/* 请求配置 */}
                  <View style={styles.section}>
                    <Text style={[styles.sectionTitle, { color: textColor, fontSize: 14, marginBottom: 8 }]}>{t('threeXUI.inboundConfig.requestConfig')}</Text>
                    <View style={styles.row}>
                      <View style={[styles.section, styles.flex1]}>
                        <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.httpRequestVersion')}</Label>
                        <Input
                          style={[styles.input, { borderColor }]}
                          value={tcpHttpRequestVersion}
                          onChangeText={setTcpHttpRequestVersion}
                          placeholder={t('threeXUI.inboundConfig.httpRequestVersionPlaceholder')}
                        />
                      </View>
                      <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                        <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.httpRequestMethod')}</Label>
                        <Input
                          style={[styles.input, { borderColor }]}
                          value={tcpHttpRequestMethod}
                          onChangeText={setTcpHttpRequestMethod}
                          placeholder={t('threeXUI.inboundConfig.httpRequestMethodPlaceholder')}
                        />
                      </View>
                    </View>
                  </View>

                  {/* 响应配置 */}
                  <View style={styles.section}>
                    <Text style={[styles.sectionTitle, { color: textColor, fontSize: 14, marginBottom: 8 }]}>{t('threeXUI.inboundConfig.responseConfig')}</Text>
                    <View style={styles.row}>
                      <View style={[styles.section, styles.flex1]}>
                        <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.httpRequestVersion')}</Label>
                        <Input
                          style={[styles.input, { borderColor }]}
                          value={tcpHttpResponseVersion}
                          onChangeText={setTcpHttpResponseVersion}
                          placeholder={t('threeXUI.inboundConfig.httpRequestVersionPlaceholder')}
                        />
                      </View>
                      <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                        <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.httpResponseStatus')}</Label>
                        <Input
                          style={[styles.input, { borderColor }]}
                          value={tcpHttpResponseStatus}
                          onChangeText={setTcpHttpResponseStatus}
                          placeholder={t('threeXUI.inboundConfig.httpResponseStatusPlaceholder')}
                        />
                      </View>
                      <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                        <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.httpResponseReason')}</Label>
                        <Input
                          style={[styles.input, { borderColor }]}
                          value={tcpHttpResponseReason}
                          onChangeText={setTcpHttpResponseReason}
                          placeholder={t('threeXUI.inboundConfig.httpResponseReasonPlaceholder')}
                        />
                      </View>
                    </View>
                  </View>

                  {/* 路径设置 */}
                  <View style={styles.section}>
                    <View style={styles.labelWithButton}>
                      <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.httpRequestPath')}</Label>
                      <TouchableOpacity
                        onPress={() => setTcpHttpPaths([...tcpHttpPaths, '/'])}
                      >
                        <PlusCircle size={16} color={textColor} />
                      </TouchableOpacity>
                    </View>
                    {tcpHttpPaths.map((path, index) => (
                      <View key={index} style={styles.pathItem}>
                        <Input
                          style={[styles.pathInput, styles.input, { borderColor }]}
                          value={path}
                          onChangeText={(text) => {
                            const newPaths = [...tcpHttpPaths];
                            newPaths[index] = text;
                            setTcpHttpPaths(newPaths);
                          }}
                          placeholder="/"
                        />
                        {tcpHttpPaths.length > 1 && (
                          <TouchableOpacity
                            style={styles.pathButton}
                            onPress={() => {
                              const newPaths = tcpHttpPaths.filter((_, i) => i !== index);
                              setTcpHttpPaths(newPaths);
                            }}
                          >
                            <Trash2 size={16} color="#ef4444" />
                          </TouchableOpacity>
                        )}
                      </View>
                    ))}
                  </View>

                  {/* 请求头设置 */}
                  <View style={styles.section}>
                    <View style={styles.labelWithButton}>
                      <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.requestHeaders')}</Label>
                      <TouchableOpacity
                        onPress={() => {
                          // 添加新的Host键值对
                          setTcpHttpRequestHeaders([
                            ...tcpHttpRequestHeaders,
                            { key: 'Host', value: '' }
                          ]);
                        }}
                      >
                        <PlusCircle size={16} color={textColor} />
                      </TouchableOpacity>
                    </View>
                    {tcpHttpRequestHeaders.map((header, index) => (
                      <View key={index} style={index !== 0 && styles.headerItem}>
                        <View style={styles.headerKeyRow}>
                          <Input
                            style={[styles.headerKeyInput, styles.input, { borderColor }]}
                            value={header.key}
                            onChangeText={(text) => {
                              const newHeaders = [...tcpHttpRequestHeaders];
                              newHeaders[index] = { ...newHeaders[index], key: text };
                              setTcpHttpRequestHeaders(newHeaders);
                            }}
                            placeholder={t('threeXUI.inboundConfig.headerKeyPlaceholder')}
                          />
                          <Input
                            style={[styles.headerValueInput, styles.input, { borderColor }]}
                            value={header.value}
                            onChangeText={(text) => {
                              const newHeaders = [...tcpHttpRequestHeaders];
                              newHeaders[index] = { ...newHeaders[index], value: text };
                              setTcpHttpRequestHeaders(newHeaders);
                            }}
                            placeholder={t('threeXUI.inboundConfig.headerValuePlaceholder')}
                          />
                          <TouchableOpacity
                            style={styles.pathButton}
                            onPress={() => {
                              const newHeaders = tcpHttpRequestHeaders.filter((_, i) => i !== index);
                              setTcpHttpRequestHeaders(newHeaders);
                            }}
                          >
                            <Trash2 size={16} color="#ef4444" />
                          </TouchableOpacity>
                        </View>
                      </View>
                    ))}
                  </View>

                  {/* 响应头设置 */}
                  <View style={styles.section}>
                    <View style={styles.labelWithButton}>
                      <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.responseHeaders')}</Label>
                      <TouchableOpacity
                        onPress={() => {
                          // 添加新的Content-Type键值对
                          setTcpHttpResponseHeaders([
                            ...tcpHttpResponseHeaders,
                            { key: 'Content-Type', value: '' }
                          ]);
                        }}
                      >
                        <PlusCircle size={16} color={textColor} />
                      </TouchableOpacity>
                    </View>
                    {tcpHttpResponseHeaders.map((header, index) => (
                      <View key={index} style={index !== 0 && styles.headerItem}>
                        <View style={styles.headerKeyRow}>
                          <Input
                            style={[styles.headerKeyInput, styles.input, { borderColor }]}
                            value={header.key}
                            onChangeText={(text) => {
                              const newHeaders = [...tcpHttpResponseHeaders];
                              newHeaders[index] = { ...newHeaders[index], key: text };
                              setTcpHttpResponseHeaders(newHeaders);
                            }}
                            placeholder={t('threeXUI.inboundConfig.headerKeyPlaceholder')}
                          />
                          <Input
                            style={[styles.headerValueInput, styles.input, { borderColor }]}
                            value={header.value}
                            onChangeText={(text) => {
                              const newHeaders = [...tcpHttpResponseHeaders];
                              newHeaders[index] = { ...newHeaders[index], value: text };
                              setTcpHttpResponseHeaders(newHeaders);
                            }}
                            placeholder={t('threeXUI.inboundConfig.headerValuePlaceholder')}
                          />
                          <TouchableOpacity
                            style={styles.pathButton}
                            onPress={() => {
                              const newHeaders = tcpHttpResponseHeaders.filter((_, i) => i !== index);
                              setTcpHttpResponseHeaders(newHeaders);
                            }}
                          >
                            <Trash2 size={16} color="#ef4444" />
                          </TouchableOpacity>
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </View>
          )}

          {/* XHTTP 设置 */}
          {transportType === 'xhttp' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.xhttpSettings')}</Text>

              {/* 主机和路径 */}
              <View style={styles.row}>
                <View style={[styles.section, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.host')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={xhttpHost}
                    onChangeText={setXhttpHost}
                    placeholder={t('threeXUI.inboundConfig.hostPlaceholder')}
                  />
                </View>
                <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.path')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={xhttpPath}
                    onChangeText={setXhttpPath}
                    placeholder={t('threeXUI.inboundConfig.pathPlaceholder')}
                  />
                </View>
              </View>

              {/* 模式选择 */}
              <View style={styles.section}>
                <SelectionGroup
                  label={t('threeXUI.inboundConfig.mode')}
                  options={[
                    { id: 'auto', label: 'Auto' },
                    { id: 'packet-up', label: 'Packet-up' },
                    { id: 'stream-up', label: 'Stream-up' },
                    { id: 'stream-one', label: 'Stream-one' }
                  ]}
                  value={xhttpMode}
                  onChange={(value) => setXhttpMode(value as string)}
                  multiple={false}
                  horizontal={true}
                  buttonSize="sm"
                  variant="secondary"
                  selectedVariant="default"
                />
              </View>

              {/* X Padding Bytes */}
              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>X Padding Bytes</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={xhttpXPaddingBytes}
                  onChangeText={setXhttpXPaddingBytes}
                  placeholder="100-1000"
                />
              </View>

              {/* 服务器专用设置 */}
              <View style={styles.section}>
                <View style={styles.switchRow}>
                  <Switch
                    checked={xhttpNoSSEHeader}
                    onCheckedChange={setXhttpNoSSEHeader}
                  />
                  <Label style={[{ color: textColor }]}>No SSE Header</Label>
                </View>
              </View>

              {/* Packet-up 和 Stream-up 模式设置 */}
              {(xhttpMode === 'packet-up' || xhttpMode === 'auto' || xhttpMode === 'stream-up') && (
                <View>
                  {(xhttpMode === 'packet-up' || xhttpMode === 'auto') && (
                    <View style={[styles.section, styles.flex1]}>
                      <Label style={[styles.label, { color: textColor }]}>SC Max Each Post Bytes</Label>
                      <Input
                        style={[styles.input, { borderColor }]}
                        value={xhttpScMaxEachPostBytes}
                        onChangeText={setXhttpScMaxEachPostBytes}
                        placeholder="1000000"
                        keyboardType="numeric"
                      />
                    </View>
                  )}

                  {(xhttpMode === 'stream-up' || xhttpMode === 'auto') && (
                    <View style={[styles.section, styles.flex1]}>
                      <Label style={[styles.label, { color: textColor }]}>SC Stream Up Server Secs</Label>
                      <Input
                        style={[styles.input, { borderColor }]}
                        value={xhttpScStreamUpServerSecs}
                        onChangeText={setXhttpScStreamUpServerSecs}
                        placeholder="20-80"
                      />
                    </View>
                  )}
                </View>
              )}

              {/* Packet-up 模式的 Max Buffered Posts */}
              {(xhttpMode === 'packet-up' || xhttpMode === 'auto') && (
                <View style={styles.section}>
                  <Label style={[styles.label, { color: textColor }]}>SC Max Buffered Posts</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={xhttpScMaxBufferedPosts}
                    onChangeText={setXhttpScMaxBufferedPosts}
                    placeholder="30"
                    keyboardType="numeric"
                  />
                </View>
              )}

              {/* Headers 管理 */}
              <View style={styles.section}>
                <View style={styles.labelWithButton}>
                  <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.customHeaders')}</Label>
                  <TouchableOpacity onPress={handleAddXhttpHeader}>
                    <PlusCircle size={16} color={textColor} />
                  </TouchableOpacity>
                </View>
                {xhttpHeaders.map((header, index) => (
                  <View key={index} style={index !== 0 && styles.headerItem}>
                    <View style={styles.headerKeyRow}>
                      <Input
                        style={[styles.headerKeyInput, styles.input, { borderColor }]}
                        value={header.key}
                        onChangeText={(value) => handleUpdateXhttpHeader(index, 'key', value)}
                        placeholder="Header Key"
                      />
                      <Input
                        style={[styles.headerValueInput, styles.input, { borderColor }]}
                        value={header.value}
                        onChangeText={(value) => handleUpdateXhttpHeader(index, 'value', value)}
                        placeholder="Header Value"
                      />
                      <TouchableOpacity
                        style={styles.pathButton}
                        onPress={() => handleRemoveXhttpHeader(index)}
                      >
                        <Trash2 size={16} color="#ef4444" />
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* mKCP 设置 */}
          {transportType === 'kcp' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.mkcpSettings')}</Text>

              <View style={styles.row}>
                <View style={[styles.section, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.mkcpMtu')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpMtu}
                    onChangeText={setMkcpMtu}
                    placeholder={t('threeXUI.inboundConfig.mkcpMtuPlaceholder')}
                    keyboardType="numeric"
                  />
                </View>
                <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.tti')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpTti}
                    onChangeText={setMkcpTti}
                    placeholder={t('threeXUI.inboundConfig.ttiPlaceholder')}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={[styles.section, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.uplinkCapacity')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpUplinkCapacity}
                    onChangeText={setMkcpUplinkCapacity}
                    placeholder={t('threeXUI.inboundConfig.uplinkCapacityPlaceholder')}
                    keyboardType="numeric"
                  />
                </View>
                <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.downlinkCapacity')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpDownlinkCapacity}
                    onChangeText={setMkcpDownlinkCapacity}
                    placeholder={t('threeXUI.inboundConfig.downlinkCapacityPlaceholder')}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={[styles.section, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.readBufferSize')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpReadBufferSize}
                    onChangeText={setMkcpReadBufferSize}
                    placeholder="1"
                    keyboardType="numeric"
                  />
                </View>
                <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.writeBufferSize')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpWriteBufferSize}
                    onChangeText={setMkcpWriteBufferSize}
                    placeholder="1"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={mkcpCongestion}
                    onCheckedChange={setMkcpCongestion}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.congestion')}</Label>
                </View>
              </View>

              <View style={styles.section}>
                <SelectionGroup
                  label={t('threeXUI.inboundConfig.masqueradingType')}
                  options={[
                    { id: 'none', label: 'None' },
                    { id: 'srtp', label: 'SRTP' },
                    { id: 'utp', label: 'uTP' },
                    { id: 'wechat-video', label: '微信视频' },
                    { id: 'dtls', label: 'DTLS' },
                    { id: 'wireguard', label: 'WireGuard' },
                    { id: 'dns', label: 'dns' }
                  ]}
                  value={mkcpHeaderType}
                  onChange={(value) => setMkcpHeaderType(value as string)}
                  multiple={false}
                  horizontal={true}
                  buttonSize="sm"
                  variant="secondary"
                  selectedVariant="default"
                />
              </View>

              {mkcpHeaderType === 'dns' && (
                <View style={styles.section}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.masqueradingDomain')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpHeaderDomain}
                    onChangeText={setMkcpHeaderDomain}
                    placeholder={t('threeXUI.inboundConfig.masqueradingDomainPlaceholder')}
                  />
                </View>
              )}

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.seedKey')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={mkcpSeed}
                  onChangeText={setMkcpSeed}
                  placeholder={t('threeXUI.inboundConfig.seedKeyPlaceholder')}
                />
              </View>
            </View>
          )}

          {/* gRPC 设置 */}
          {transportType === 'grpc' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.grpcSettings')}</Text>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.authority')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={grpcAuthority}
                  onChangeText={setGrpcAuthority}
                  placeholder={t('threeXUI.inboundConfig.authorityPlaceholder')}
                />
              </View>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.serviceName')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={grpcServiceName}
                  onChangeText={setGrpcServiceName}
                  placeholder={t('threeXUI.inboundConfig.serviceNamePlaceholder')}
                />
              </View>
              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.initialWindowsSize')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={grpcInitialWindowsSize}
                  onChangeText={setGrpcInitialWindowsSize}
                  placeholder={t('threeXUI.inboundConfig.initialWindowsSizePlaceholder')}
                  keyboardType="numeric"
                />
              </View>


            </View>
          )}

          {/* WebSocket 设置 */}
          {transportType === 'ws' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.websocketSettings')}</Text>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={wsAcceptProxyProtocol}
                    onCheckedChange={setWsAcceptProxyProtocol}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.acceptProxyProtocol')}</Label>
                </View>
              </View>

              <View style={styles.row}>
                <View style={[styles.section, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.host')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={wsHost}
                    onChangeText={setWsHost}
                    placeholder={t('threeXUI.inboundConfig.hostPlaceholder')}
                  />
                </View>
                <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.path')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={wsPath}
                    onChangeText={setWsPath}
                    placeholder={t('threeXUI.inboundConfig.pathPlaceholder')}
                  />
                </View>
              </View>

              {/* WebSocket Headers 设置 */}


              <View style={styles.section}>
                <Label style={[styles.label,{ color: textColor }]}>{t('threeXUI.inboundConfig.heartbeatPeriod')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={wsHeartbeatPeriod}
                  onChangeText={setWsHeartbeatPeriod}
                  placeholder={t('threeXUI.inboundConfig.heartbeatPeriodPlaceholder')}
                  keyboardType="numeric"
                />
              </View>
            </View>
          )}

          {/* HTTP Upgrade 设置 */}
          {transportType === 'httpupgrade' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.httpUpgradeSettings')}</Text>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={httpUpgradeAcceptProxyProtocol}
                    onCheckedChange={setHttpUpgradeAcceptProxyProtocol}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.acceptProxyProtocol')}</Label>
                </View>
              </View>

              <View style={styles.row}>
                <View style={[styles.section, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.host')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={httpUpgradeHost}
                    onChangeText={setHttpUpgradeHost}
                    placeholder={t('threeXUI.inboundConfig.hostPlaceholder')}
                  />
                </View>
                <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.path')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={httpUpgradePath}
                    onChangeText={setHttpUpgradePath}
                    placeholder={t('threeXUI.inboundConfig.pathPlaceholder')}
                  />
                </View>
              </View>

              {/* HTTP Upgrade Headers 设置 */}

            </View>
          )}
        </BottomSheetKeyboardAwareScrollView>
      </BottomSheetModal>

      {/* 安全设置底部弹窗 */}
      <BottomSheetModal
        ref={securityBottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
        containerComponent={WindowOverlay}
      >
        <BottomSheetKeyboardAwareScrollView style={[styles.bottomSheetContent, { backgroundColor }]} bottomOffset={50}>
          <Text style={[styles.bottomSheetTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.securitySettings')}</Text>

          {/* 安全类型选择 */}
          <View style={styles.section}>
            <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.securityType')}</Label>
            <SelectionGroup
              options={[
                { id: 'none', label: 'None' },
                { id: 'tls', label: 'TLS' },
                {
                  id: 'reality',
                  label: 'Reality',
                  disabled: !['vless', 'trojan'].includes(protocol)
                }
              ]}
              value={securityType}
              onChange={(value) => setSecurityType(value as string)}
              horizontal={true}
            />
            {!['vless', 'trojan'].includes(protocol) && securityType === 'none' && (
              <Text style={[styles.placeholder, { color: textColor + '80', fontSize: 12, marginTop: 4 }]}>
                {t('threeXUI.inboundConfig.realityProtocolNote')}
              </Text>
            )}
          </View>

          {/* TLS 设置 */}
          {securityType === 'tls' && (
            <>
              <Text style={[styles.sectionTitle, styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.tlsSettings')}</Text>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={tlsRejectUnknownSni}
                    onCheckedChange={setTlsRejectUnknownSni}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.rejectUnknownSni')}</Label>
                </View>
              </View>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={tlsAllowInsecure}
                    onCheckedChange={setTlsAllowInsecure}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.allowInsecure')}</Label>
                </View>
              </View>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.alpnProtocol')}</Label>
                <SelectionGroup
                  options={[
                    { id: 'h2', label: 'h2' },
                    { id: 'http/1.1', label: 'http/1.1' },
                    { id: 'h3', label: 'h3' }
                  ]}
                  value={tlsAlpn}
                  onChange={(value) => setTlsAlpn(value as string[])}
                  multiple={true}
                  horizontal={true}
                />
              </View>

              <View style={[styles.row,styles.section]}>
                <View style={styles.flex1}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.minVersion')}</Label>
                  <Select value={{ value: tlsMinVersion, label: tlsMinVersion }} onValueChange={(option) => setTlsMinVersion(option?.value || '1.2')}>
                    <SelectTrigger style={[styles.input, { borderColor }]}>
                      <SelectValue className='text-foreground text-sm native:text-lg' placeholder={t('threeXUI.inboundConfig.selectVersion')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1.0" label="1.0">1.0</SelectItem>
                      <SelectItem value="1.1" label="1.1">1.1</SelectItem>
                      <SelectItem value="1.2" label="1.2">1.2</SelectItem>
                      <SelectItem value="1.3" label="1.3">1.3</SelectItem>
                    </SelectContent>
                  </Select>
                </View>
                <View style={[styles.flex1, styles.marginLeft, ]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.maxVersion')}</Label>
                  <Select value={{ value: tlsMaxVersion, label: tlsMaxVersion }} onValueChange={(option) => setTlsMaxVersion(option?.value || '1.3')}>
                    <SelectTrigger style={[styles.input, { borderColor }]}>
                      <SelectValue className='text-foreground text-sm native:text-lg' placeholder={t('threeXUI.inboundConfig.selectVersion')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1.0" label="1.0">1.0</SelectItem>
                      <SelectItem value="1.1" label="1.1">1.1</SelectItem>
                      <SelectItem value="1.2" label="1.2">1.2</SelectItem>
                      <SelectItem value="1.3" label="1.3">1.3</SelectItem>
                    </SelectContent>
                  </Select>
                </View>
              </View>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={tlsDisableSystemRoot}
                    onCheckedChange={setTlsDisableSystemRoot}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.disableSystemRoot')}</Label>
                </View>
              </View>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={tlsEnableSessionResumption}
                    onCheckedChange={setTlsEnableSessionResumption}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.enableSessionResumption')}</Label>
                </View>
              </View>

              {/* 证书管理 */}
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <Label style={[styles.sectionTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.certificates')}</Label>
                  <Button onPress={handleAddCert} size="sm" variant="secondary">
                    <Text style={[styles.editButtonText, { color: textColor }]}>{t('threeXUI.inboundConfig.addCertificate')}</Text>
                  </Button>
                </View>

                {tlsCertificates.length === 0 ? (
                  <Text style={[styles.placeholder, { color: textColor + '80' }]}>
                    {t('threeXUI.inboundConfig.noCertificates')}
                  </Text>
                ) : (
                  <View style={styles.userList}>
                    {tlsCertificates.map((cert, index) => (
                      <View key={index} style={[styles.userCard, { borderColor }]}>
                        <View style={styles.userInfo}>
                          <Text style={[styles.userEmail, { color: textColor }]}>
                            {`cert${index + 1}`}
                          </Text>
                        </View>
                        <View style={styles.userActions}>
                          <TouchableOpacity onPress={() => handleEditCert(cert, index)} style={styles.actionButton}>
                            <Edit size={16} color={textColor} />
                          </TouchableOpacity>
                          <TouchableOpacity onPress={() => handleDeleteCert(index)} style={styles.actionButton}>
                            <Trash2 size={16} color="#ef4444" />
                          </TouchableOpacity>
                        </View>
                      </View>
                    ))}
                  </View>
                )}
              </View>
            </>
          )}

          {/* Reality 设置 */}
          {securityType === 'reality' && (
            <>
              <Text style={[styles.sectionTitle, styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.realitySettings')}</Text>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.target')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realityTarget}
                  onChangeText={setRealityTarget}
                  placeholder={t('threeXUI.inboundConfig.targetPlaceholder')}
                />
              </View>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.serverNames')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realityServerNames}
                  onChangeText={setRealityServerNames}
                  placeholder={t('threeXUI.inboundConfig.serverNamesPlaceholder')}
                />
              </View>

              <View style={styles.row}>
                <View style={[styles.section, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>Xver</Label>
                  <Select value={{ value: realityXver.toString(), label: realityXver.toString() }} onValueChange={(option) => setRealityXver(parseInt(option?.value || '0'))}>
                    <SelectTrigger style={[styles.input, { borderColor }]}>
                      <SelectValue className='text-foreground text-sm native:text-lg' placeholder={t('threeXUI.inboundConfig.selectXver')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0" label="0">0</SelectItem>
                      <SelectItem value="1" label="1">1</SelectItem>
                      <SelectItem value="2" label="2">2</SelectItem>
                    </SelectContent>
                  </Select>
                </View>
                <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.maxTimeDiff')}</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={realityMaxTimeDiff.toString()}
                    onChangeText={(text) => setRealityMaxTimeDiff(parseInt(text) || 0)}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.section}>
                <View style={styles.labelWithButton}>
                  <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.privateKey')}</Label>
                  <TouchableOpacity onPress={handleGenerateRealityKeys}>
                    <RefreshCcw size={16} color={textColor} />
                  </TouchableOpacity>
                </View>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realityPrivateKey}
                  onChangeText={setRealityPrivateKey}
                  placeholder={t('threeXUI.inboundConfig.realityPrivateKeyPlaceholder')}
                />
              </View>

              <View style={styles.section}>
                <Label style={[styles.label,{ color: textColor }]}>{t('threeXUI.inboundConfig.realityPublicKeyLabel')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realityPublicKey}
                  onChangeText={setRealityPublicKey}
                  placeholder={t('threeXUI.inboundConfig.realityPublicKeyPlaceholder')}
                />
              </View>



              <View style={styles.section}>
                <View style={styles.labelWithButton}>
                  <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.shortIds')}</Label>
                  <TouchableOpacity onPress={generateRandomShortIds}>
                    <RefreshCcw size={16} color={textColor} />
                  </TouchableOpacity>
                </View>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realityShortIds.join(', ')}
                  onChangeText={(text) => setRealityShortIds(text.split(',').map(s => s.trim()).filter(s => s))}
                  placeholder={t('threeXUI.inboundConfig.shortIdsPlaceholder')}
                />
              </View>

              {/* Limit Fallback Upload 设置 */}
              <Text style={[styles.sectionTitle, styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.limitFallbackUpload')}</Text>
              <View style={styles.row}>
                <View style={[styles.section, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>After Bytes</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={realityLimitFallbackUpload.afterBytes.toString()}
                    onChangeText={(text) => setRealityLimitFallbackUpload(prev => ({ ...prev, afterBytes: parseInt(text) || 0 }))}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>
                <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>Bytes Per Sec</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={realityLimitFallbackUpload.bytesPerSec.toString()}
                    onChangeText={(text) => setRealityLimitFallbackUpload(prev => ({ ...prev, bytesPerSec: parseInt(text) || 0 }))}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>
              </View>
              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>Burst Bytes Per Sec</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realityLimitFallbackUpload.burstBytesPerSec.toString()}
                  onChangeText={(text) => setRealityLimitFallbackUpload(prev => ({ ...prev, burstBytesPerSec: parseInt(text) || 0 }))}
                  placeholder="0"
                  keyboardType="numeric"
                />
              </View>

              {/* Limit Fallback Download 设置 */}
              <Text style={[styles.sectionTitle, styles.sectionHeader, { color: textColor }]}>{t('threeXUI.inboundConfig.limitFallbackDownload')}</Text>
              <View style={styles.row}>
                <View style={[styles.section, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>After Bytes</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={realityLimitFallbackDownload.afterBytes.toString()}
                    onChangeText={(text) => setRealityLimitFallbackDownload(prev => ({ ...prev, afterBytes: parseInt(text) || 0 }))}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>
                <View style={[styles.section, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>Bytes Per Sec</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={realityLimitFallbackDownload.bytesPerSec.toString()}
                    onChangeText={(text) => setRealityLimitFallbackDownload(prev => ({ ...prev, bytesPerSec: parseInt(text) || 0 }))}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>
              </View>
              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>Burst Bytes Per Sec</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realityLimitFallbackDownload.burstBytesPerSec.toString()}
                  onChangeText={(text) => setRealityLimitFallbackDownload(prev => ({ ...prev, burstBytesPerSec: parseInt(text) || 0 }))}
                  placeholder="0"
                  keyboardType="numeric"
                />
              </View>
            </>
          )}
        </BottomSheetKeyboardAwareScrollView>
      </BottomSheetModal>

      {/* SockOpt设置底部弹窗 */}
      <BottomSheetModal
        ref={sockOptBottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
        containerComponent={WindowOverlay}
      >
        <BottomSheetKeyboardAwareScrollView style={[styles.bottomSheetContent, { backgroundColor }]} bottomOffset={50}>
          <Text style={[styles.bottomSheetTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.sockOptSettingsTitle')}</Text>

          {/* Mark 和 TCP Max Seg 一行 */}
          <View style={styles.row}>
            <View style={[styles.section, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>Mark</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptMark}
                onChangeText={setSockOptMark}
                placeholder="0"
                keyboardType="numeric"
              />
            </View>
            <View style={[styles.section, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>TCP Max Seg</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpMaxSeg}
                onChangeText={setSockOptTcpMaxSeg}
                placeholder="1440"
                keyboardType="numeric"
              />
            </View>
          </View>

          {/* TCP Fast Open */}
          <View style={styles.bottomSheetSection}>
            <View style={styles.switchRowCompact}>
              <Switch
                checked={sockOptTcpFastOpen}
                onCheckedChange={setSockOptTcpFastOpen}
              />
              <Label style={[styles.labelRight, { color: textColor }]}>TCP Fast Open</Label>
            </View>
          </View>

          {/* TProxy 和 Domain Strategy 一行 */}
          <View style={styles.row}>
            <View style={[styles.section, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>TProxy</Label>
              <Select value={{ value: sockOptTproxy, label: sockOptTproxy }} onValueChange={(option) => setSockOptTproxy((option?.value || option) as string)}>
                <SelectTrigger style={[styles.input, { borderColor }]}>
                  <SelectValue className='text-foreground text-sm native:text-lg' placeholder={t('threeXUI.inboundConfig.selectTproxyMode')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="off" label="Off">Off</SelectItem>
                  <SelectItem value="redirect" label="Redirect">Redirect</SelectItem>
                  <SelectItem value="tproxy" label="TProxy">TProxy</SelectItem>
                </SelectContent>
              </Select>
            </View>
            <View style={[styles.section, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>Domain Strategy</Label>
              <Select value={{ value: sockOptDomainStrategy, label: sockOptDomainStrategy }} onValueChange={(option) => setSockOptDomainStrategy((option?.value || option) as string)}>
                <SelectTrigger style={[styles.input, { borderColor }]}>
                  <SelectValue className='text-foreground text-sm native:text-lg' placeholder={t('threeXUI.inboundConfig.selectDomainStrategy')} />
                </SelectTrigger>
                <SelectContent>
                  <ScrollView>
                  <SelectItem value="AsIs" label="AsIs">AsIs</SelectItem>
                  <SelectItem value="UseIP" label="UseIP">UseIP</SelectItem>
                  <SelectItem value="UseIPv6v4" label="UseIPv6v4">UseIPv6v4</SelectItem>
                  <SelectItem value="UseIPv6" label="UseIPv6">UseIPv6</SelectItem>
                  <SelectItem value="UseIPv4v6" label="UseIPv4v6">UseIPv4v6</SelectItem>
                  <SelectItem value="UseIPv4" label="UseIPv4">UseIPv4</SelectItem>
                  <SelectItem value="ForceIP" label="ForceIP">ForceIP</SelectItem>
                  <SelectItem value="ForceIPv6v4" label="ForceIPv6v4">ForceIPv6v4</SelectItem>
                  <SelectItem value="ForceIPv6" label="ForceIPv6">ForceIPv6</SelectItem>
                  <SelectItem value="ForceIPv4v6" label="ForceIPv4v6">ForceIPv4v6</SelectItem>
                  <SelectItem value="ForceIPv4" label="ForceIPv4">ForceIPv4</SelectItem>
                  </ScrollView>
                </SelectContent>
              </Select>
            </View>
          </View>

          {/* Dialer Proxy */}
          <View style={styles.bottomSheetSection}>
            <Label style={[styles.label, { color: textColor }]}>Dialer Proxy</Label>
            <Input
              style={[styles.input, { borderColor }]}
              value={sockOptDialerProxy}
              onChangeText={setSockOptDialerProxy}
              placeholder={"tag"}
            />
          </View>

          {/* Accept Proxy Protocol */}
          <View style={styles.bottomSheetSection}>
            <View style={styles.switchRowCompact}>
              <Switch
                checked={sockOptAcceptProxyProtocol}
                onCheckedChange={setSockOptAcceptProxyProtocol}
              />
              <Label style={[styles.labelRight, { color: textColor }]}>Accept Proxy Protocol</Label>
            </View>
          </View>

          {/* V6 Only */}
          <View style={styles.bottomSheetSection}>
            <View style={styles.switchRowCompact}>
              <Switch
                checked={sockOptV6Only}
                onCheckedChange={setSockOptV6Only}
              />
              <Label style={[styles.labelRight, { color: textColor }]}>V6 Only</Label>
            </View>
          </View>

          {/* TCP Keep Alive Interval 和 TCP Keep Alive Idle 一行 */}
          <View style={styles.row}>
            <View style={[styles.section, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>TCP Keep Alive Interval</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpKeepAliveInterval}
                onChangeText={setSockOptTcpKeepAliveInterval}
                placeholder="0"
                keyboardType="numeric"
              />
            </View>
            <View style={[styles.section, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>TCP Keep Alive Idle</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpKeepAliveIdle}
                onChangeText={setSockOptTcpKeepAliveIdle}
                placeholder="300"
                keyboardType="numeric"
              />
            </View>
          </View>

          {/* TCP User Timeout 和 TCP Congestion 一行 */}
          <View style={styles.row}>
            <View style={[styles.section, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>TCP User Timeout</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpUserTimeout}
                onChangeText={setSockOptTcpUserTimeout}
                placeholder="10000"
                keyboardType="numeric"
              />
            </View>
            <View style={[styles.section, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>TCP Congestion</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpCongestion}
                onChangeText={setSockOptTcpCongestion}
                placeholder="bbr"
              />
            </View>
          </View>

          {/* Interface 和 TCP Window Clamp 一行 */}
          <View style={styles.row}>
            <View style={[styles.section, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>Interface</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptInterface}
                onChangeText={setSockOptInterface}
                placeholder="wg0"
              />
            </View>
            <View style={[styles.section, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>TCP Window Clamp</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpWindowClamp}
                onChangeText={setSockOptTcpWindowClamp}
                placeholder="600"
                keyboardType="numeric"
              />
            </View>
          </View>

          {/* Address Port Strategy */}
          <View style={styles.bottomSheetSection}>
            <Label style={[styles.label, { color: textColor }]}>Address Port Strategy</Label>
            <Select value={{ value: sockOptAddressPortStrategy, label: sockOptAddressPortStrategy || 'none' }} onValueChange={(option) => setSockOptAddressPortStrategy((option?.value || option) as string)}>
              <SelectTrigger style={[styles.input, { borderColor }]}>
                <SelectValue className='text-foreground text-sm native:text-lg' placeholder={t('threeXUI.inboundConfig.selectAddressPortStrategy')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none" label="None">None</SelectItem>
                <SelectItem value="SrvPortOnly" label="SrvPortOnly">SrvPortOnly</SelectItem>
                <SelectItem value="SrvAddressOnly" label="SrvAddressOnly">SrvAddressOnly</SelectItem>
                <SelectItem value="SrvPortAndAddress" label="SrvPortAndAddress">SrvPortAndAddress</SelectItem>
                <SelectItem value="TxtPortOnly" label="TxtPortOnly">TxtPortOnly</SelectItem>
                <SelectItem value="TxtAddressOnly" label="TxtAddressOnly">TxtAddressOnly</SelectItem>
                <SelectItem value="TxtPortAndAddress" label="TxtPortAndAddress">TxtPortAndAddress</SelectItem>
              </SelectContent>
            </Select>
          </View>
        </BottomSheetKeyboardAwareScrollView>
      </BottomSheetModal>

      {/* Sniffing设置底部弹窗 */}
      <BottomSheetModal
        ref={sniffingBottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
        containerComponent={WindowOverlay}
      >
        <BottomSheetKeyboardAwareScrollView style={[styles.bottomSheetContent, { backgroundColor }]} bottomOffset={50}>
          <Text style={[styles.bottomSheetTitle, { color: textColor }]}>{t('threeXUI.inboundConfig.sniffingSettingsTitle')}</Text>

          {/* 目标覆盖选择 */}
          <View style={styles.section}>
            <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.destOverrideLabel')}</Label>
            <SelectionGroup
              options={[
                { id: 'http', label: 'HTTP' },
                { id: 'tls', label: 'TLS' },
                { id: 'quic', label: 'QUIC' },
                { id: 'fakedns', label: 'FakeDNS' }
              ]}
              value={sniffingDestOverride}
              onChange={(value) => setSniffingDestOverride(value as string[])}
              multiple={true}
              horizontal={true}
            />
          </View>

          {/* 仅元数据开关 */}
          <View style={styles.section}>
            <View style={styles.switchRowCompact}>
              <Switch
                checked={sniffingMetadataOnly}
                onCheckedChange={setSniffingMetadataOnly}
              />
              <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.metadataOnlyLabel')}</Label>
            </View>
          </View>

          {/* 仅路由开关 */}
          <View style={styles.section}>
            <View style={styles.switchRowCompact}>
              <Switch
                checked={sniffingRouteOnly}
                onCheckedChange={setSniffingRouteOnly}
              />
              <Label style={[styles.labelRight, { color: textColor }]}>{t('threeXUI.inboundConfig.routeOnlyLabel')}</Label>
            </View>
          </View>

          {/* 域名排除列表 */}
          <View style={styles.section}>
            <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.domainsExcluded')}</Label>
            <Input
              style={[styles.input, { borderColor }]}
              value={sniffingDomainsExcluded.join(', ')}
              onChangeText={(value) => {
                const domains = value.split(',').map(domain => domain.trim()).filter(domain => domain);
                setSniffingDomainsExcluded(domains);
              }}
              placeholder={t('threeXUI.inboundConfig.domainsExcludedPlaceholder')}
            />
          </View>
        </BottomSheetKeyboardAwareScrollView>
      </BottomSheetModal>

      {/* 证书设置底部弹窗 */}
      <BottomSheetModal
        ref={certBottomSheetRef}
        index={1}
        snapPoints={snapPoint}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
        containerComponent={WindowOverlay}
      >
        <BottomSheetKeyboardAwareScrollView style={[styles.bottomSheetContent, { backgroundColor }]} bottomOffset={50}>
          <Text style={[styles.bottomSheetTitle, { color: textColor }]}>
            {editingCert ? t('threeXUI.inboundConfig.editCertificateTitle') : t('threeXUI.inboundConfig.addCertificateTitle')}
          </Text>

          {/* 添加方式选择 */}
          <View style={styles.bottomSheetSection}>
            <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.certAddMethod')}</Label>
            <SelectionGroup
              options={[
                { id: 'content', label: t('threeXUI.inboundConfig.certFileContent') },
                { id: 'file', label: t('threeXUI.inboundConfig.certFilePath') }
              ]}
              value={certAddMethod}
              onChange={(value) => setCertAddMethod(value as 'file' | 'content')}
              horizontal={true}
            />
          </View>

          {certAddMethod === 'file' ? (
            <>
              {/* 证书文件路径 */}
              <View style={styles.bottomSheetSection}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.certificateFilePath')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={certFile}
                  onChangeText={setCertFile}
                  placeholder="/path/to/certificate.crt"
                />
              </View>

              {/* 密钥文件路径 */}
              <View style={styles.bottomSheetSection}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.keyFilePath')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={certKeyFile}
                  onChangeText={setCertKeyFile}
                  placeholder="/path/to/key.key"
                />
              </View>
            </>
          ) : (
            <>
              {/* 证书内容 */}
              <View style={styles.bottomSheetSection}>
                <View style={styles.labelWithButton}>
                  <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.certificateContent')}</Label>
                  <TouchableOpacity onPress={handleGenerateTLSCertificate}>
                    <RefreshCcw size={16} color={textColor} />
                  </TouchableOpacity>
                </View>
                <Input
                  style={[styles.input, { borderColor, height: 120 }]}
                  value={certContent}
                  onChangeText={setCertContent}
                  placeholder={`-----BEGIN CERTIFICATE-----
...
-----END CERTIFICATE-----`}
                  multiline
                  textAlignVertical="top"
                />
              </View>

              {/* 密钥内容 */}
              <View style={styles.bottomSheetSection}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.certKeyContentLabel')}</Label>
                <Input
                  style={[styles.input, { borderColor, height: 120 }]}
                  value={certKeyContent}
                  onChangeText={setCertKeyContent}
                  placeholder={`-----BEGIN RSA PRIVATE KEY-----
...
-----END RSA PRIVATE KEY-----`}
                  multiline
                  textAlignVertical="top"
                />
              </View>
            </>
          )}

        </BottomSheetKeyboardAwareScrollView>

        {/* 固定底部按钮 */}
        <View style={[styles.bottomSheetFooter, { borderTopColor: borderColor, backgroundColor }]}>
          <Button onPress={handleSaveCert} style={styles.saveButton}>
            <Text style={styles.saveButtonText}>{t('common.save')}</Text>
          </Button>
        </View>
      </BottomSheetModal>

      {/* 保存按钮 */}
      <View style={[styles.footer, { borderTopColor: borderColor, backgroundColor }]}>
        <Button onPress={handleSave} style={[styles.saveButton, {opacity: isSaving ? 0.5 : 1}]} disabled={isSaving}>
          <Text style={styles.saveButtonText}>
            {isSaving
              ? t('common.saving')
              : (isEditMode ? t('threeXUI.inboundConfig.update') : t('threeXUI.inboundConfig.save'))
            }
          </Text>
        </Button>
      </View>
    </SafeAreaView>

      {/* 回落编辑对话框 */}
      <Modal
        visible={showFallbackDialog}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowFallbackDialog(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor }]}>
            {/* 标题 */}
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: textColor }]}>
                {editingFallback ? t('threeXUI.inboundConfig.editTitle') : t('threeXUI.inboundConfig.addFallback')}
              </Text>
            </View>

            <KeyboardAwareScrollView style={styles.modalBody} showsVerticalScrollIndicator={false} bottomOffset={50}>
              {/* 名称 */}
              <View style={styles.dialogSection}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.fallbackName')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={fallbackName}
                  onChangeText={setFallbackName}
                  placeholder={t('threeXUI.inboundConfig.fallbackNamePlaceholder')}
                />
              </View>

              {/* ALPN */}
              <View style={styles.dialogSection}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.fallbackAlpn')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={fallbackAlpn}
                  onChangeText={setFallbackAlpn}
                  placeholder={t('threeXUI.inboundConfig.fallbackAlpnPlaceholder')}
                />
              </View>

              {/* 路径 */}
              <View style={styles.dialogSection}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.fallbackPath')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={fallbackPath}
                  onChangeText={setFallbackPath}
                  placeholder={t('threeXUI.inboundConfig.fallbackPathPlaceholder')}
                />
              </View>

              {/* 目标端口 */}
              <View style={styles.dialogSection}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.fallbackDest')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={fallbackDest}
                  onChangeText={setFallbackDest}
                  placeholder={t('threeXUI.inboundConfig.fallbackDestPlaceholder')}
                  keyboardType="numeric"
                />
              </View>

              {/* Xver */}
              <View style={styles.dialogSection}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.fallbackXver')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={fallbackXver}
                  onChangeText={setFallbackXver}
                  placeholder={t('threeXUI.inboundConfig.fallbackXverPlaceholder')}
                  keyboardType="numeric"
                />
              </View>
            </KeyboardAwareScrollView>

            <View style={styles.modalButtons}>
              <Button variant="outline" onPress={() => setShowFallbackDialog(false)}>
                <Text>{t('threeXUI.inboundConfig.cancel')}</Text>
              </Button>
              <Button onPress={handleSaveFallback}>
                <Text>{t('threeXUI.inboundConfig.save')}</Text>
              </Button>
            </View>
          </View>
        </View>
      </Modal>

      {/* Peer 编辑对话框 */}
      <Modal
        visible={showPeerDialog}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowPeerDialog(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor }]}>
            {/* 标题 */}
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: textColor }]}>
                {editingPeer ? t('threeXUI.inboundConfig.editTitle') : t('threeXUI.inboundConfig.addPeer')}
              </Text>
            </View>

            <KeyboardAwareScrollView style={styles.modalBody} showsVerticalScrollIndicator={false} bottomOffset={50}>
              {/* 公钥 */}
              <View style={styles.dialogSection}>
                <View style={styles.labelWithButton}>
                  <Label style={{ color: textColor }}>{t('threeXUI.inboundConfig.publicKey')}</Label>
                  <TouchableOpacity onPress={handleGeneratePeerKeys}>
                    <RefreshCcw size={16} color={textColor} />
                  </TouchableOpacity>
                </View>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={peerPublicKey}
                  onChangeText={setPeerPublicKey}
                  placeholder={t('threeXUI.inboundConfig.publicKeyPlaceholder')}
                />
              </View>

              {/* 私钥 */}
              <View style={styles.dialogSection}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.privateKey')} ({t('threeXUI.inboundConfig.generate')})</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={peerPrivateKey}
                  placeholder={t('threeXUI.inboundConfig.privateKeyPlaceholder')}
                  editable={false}
                />
              </View>

              {/* 允许的 IP */}
              <View style={styles.dialogSection}>
                <Label style={[styles.label, { color: textColor }]}>{t('threeXUI.inboundConfig.allowedIPs')}</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={peerAllowedIPs}
                  onChangeText={setPeerAllowedIPs}
                  placeholder={t('threeXUI.inboundConfig.allowedIPsPlaceholder')}
                />
              </View>
            </KeyboardAwareScrollView>

            <View style={styles.modalButtons}>
              <Button variant="outline" onPress={() => setShowPeerDialog(false)}>
                <Text>{t('threeXUI.inboundConfig.cancel')}</Text>
              </Button>
              <Button onPress={handleSavePeer}>
                <Text>{t('threeXUI.inboundConfig.save')}</Text>
              </Button>
            </View>
          </View>
        </View>
      </Modal>

    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom:36
  },
  section: {
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
  },
  flex1: {
    flex: 1,
  },
  marginLeft: {
    marginLeft: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },

  userList: {
    gap: 8,
  },
  userCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
  },
  userInfo: {
    flex: 1,
  },
  userEmail: {
    fontSize: 14,
    fontWeight: '500',
  },
  userId: {
    fontSize: 12,
  },
  userActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 4,
  },
  placeholder: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 13,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
    borderRadius: 12,
    padding: 0,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  modalBody: {
    padding: 20,
    maxHeight: 400,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  dialogSection: {
    gap: 8,
    marginBottom: 16,
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },

  editButtonText: {
    fontSize: 12,
  },
  transportInfo: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    gap: 4,
  },
  transportType: {
    fontSize: 14,
    fontWeight: '500',
  },
  transportDetail: {
    fontSize: 12,
  },
  sockOptControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  disabledButton: {
    opacity: 0.5,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    borderTopWidth: 1,
  },
  saveButton: {
    width: '100%',
  },
  saveButtonText: {
    fontWeight: '600',
  },
  bottomSheetContent: {
    padding: 16,
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  labelWithButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    fontSize: 14,
    fontWeight: '500',
    marginBottom:8
  },
  xtlsFlowRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  xtlsFlowLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  switchRowCompact: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  labelRight: {
    fontSize: 14,
    fontWeight: '500',
  },
  pathItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  pathInput: {
    flex: 1,
  },
  pathButton: {
    padding: 8,
  },
  headerItem: {
    marginTop: 8,
  },
  headerKeyRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerKeyInput: {
    flex: 1,
  },
  headerValueItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
    marginLeft: 16,
  },
  headerValueInput: {
    flex: 1,
  },
  addButton: {
    marginTop: 8,
  },
  bottomSheetSection: {
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  bottomSheetFooter: {
    padding: 16,
    borderTopWidth: 1,
  },
});
